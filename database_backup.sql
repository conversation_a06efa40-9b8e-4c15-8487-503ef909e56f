--
-- PostgreSQL database dump
--

-- Dumped from database version 17.5
-- Dumped by pg_dump version 17.5

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: admin_permissions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.admin_permissions (
    id integer NOT NULL,
    document_id character varying(255),
    action character varying(255),
    action_parameters jsonb,
    subject character varying(255),
    properties jsonb,
    conditions jsonb,
    created_at timestamp(6) without time zone,
    updated_at timestamp(6) without time zone,
    published_at timestamp(6) without time zone,
    created_by_id integer,
    updated_by_id integer,
    locale character varying(255)
);


ALTER TABLE public.admin_permissions OWNER TO postgres;

--
-- Name: admin_permissions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.admin_permissions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.admin_permissions_id_seq OWNER TO postgres;

--
-- Name: admin_permissions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.admin_permissions_id_seq OWNED BY public.admin_permissions.id;


--
-- Name: admin_permissions_role_lnk; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.admin_permissions_role_lnk (
    id integer NOT NULL,
    permission_id integer,
    role_id integer,
    permission_ord double precision
);


ALTER TABLE public.admin_permissions_role_lnk OWNER TO postgres;

--
-- Name: admin_permissions_role_lnk_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.admin_permissions_role_lnk_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.admin_permissions_role_lnk_id_seq OWNER TO postgres;

--
-- Name: admin_permissions_role_lnk_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.admin_permissions_role_lnk_id_seq OWNED BY public.admin_permissions_role_lnk.id;


--
-- Name: admin_roles; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.admin_roles (
    id integer NOT NULL,
    document_id character varying(255),
    name character varying(255),
    code character varying(255),
    description character varying(255),
    created_at timestamp(6) without time zone,
    updated_at timestamp(6) without time zone,
    published_at timestamp(6) without time zone,
    created_by_id integer,
    updated_by_id integer,
    locale character varying(255)
);


ALTER TABLE public.admin_roles OWNER TO postgres;

--
-- Name: admin_roles_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.admin_roles_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.admin_roles_id_seq OWNER TO postgres;

--
-- Name: admin_roles_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.admin_roles_id_seq OWNED BY public.admin_roles.id;


--
-- Name: admin_users; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.admin_users (
    id integer NOT NULL,
    document_id character varying(255),
    firstname character varying(255),
    lastname character varying(255),
    username character varying(255),
    email character varying(255),
    password character varying(255),
    reset_password_token character varying(255),
    registration_token character varying(255),
    is_active boolean,
    blocked boolean,
    prefered_language character varying(255),
    created_at timestamp(6) without time zone,
    updated_at timestamp(6) without time zone,
    published_at timestamp(6) without time zone,
    created_by_id integer,
    updated_by_id integer,
    locale character varying(255)
);


ALTER TABLE public.admin_users OWNER TO postgres;

--
-- Name: admin_users_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.admin_users_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.admin_users_id_seq OWNER TO postgres;

--
-- Name: admin_users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.admin_users_id_seq OWNED BY public.admin_users.id;


--
-- Name: admin_users_roles_lnk; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.admin_users_roles_lnk (
    id integer NOT NULL,
    user_id integer,
    role_id integer,
    role_ord double precision,
    user_ord double precision
);


ALTER TABLE public.admin_users_roles_lnk OWNER TO postgres;

--
-- Name: admin_users_roles_lnk_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.admin_users_roles_lnk_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.admin_users_roles_lnk_id_seq OWNER TO postgres;

--
-- Name: admin_users_roles_lnk_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.admin_users_roles_lnk_id_seq OWNED BY public.admin_users_roles_lnk.id;


--
-- Name: doi_collections; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.doi_collections (
    id integer NOT NULL,
    document_id character varying(255),
    doi character varying(255),
    created_at timestamp(6) without time zone,
    updated_at timestamp(6) without time zone,
    published_at timestamp(6) without time zone,
    created_by_id integer,
    updated_by_id integer,
    locale character varying(255)
);


ALTER TABLE public.doi_collections OWNER TO postgres;

--
-- Name: doi_collections_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.doi_collections_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.doi_collections_id_seq OWNER TO postgres;

--
-- Name: doi_collections_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.doi_collections_id_seq OWNED BY public.doi_collections.id;


--
-- Name: files; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.files (
    id integer NOT NULL,
    document_id character varying(255),
    name character varying(255),
    alternative_text character varying(255),
    caption character varying(255),
    width integer,
    height integer,
    formats jsonb,
    hash character varying(255),
    ext character varying(255),
    mime character varying(255),
    size numeric(10,2),
    url character varying(255),
    preview_url character varying(255),
    provider character varying(255),
    provider_metadata jsonb,
    folder_path character varying(255),
    created_at timestamp(6) without time zone,
    updated_at timestamp(6) without time zone,
    published_at timestamp(6) without time zone,
    created_by_id integer,
    updated_by_id integer,
    locale character varying(255)
);


ALTER TABLE public.files OWNER TO postgres;

--
-- Name: files_folder_lnk; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.files_folder_lnk (
    id integer NOT NULL,
    file_id integer,
    folder_id integer,
    file_ord double precision
);


ALTER TABLE public.files_folder_lnk OWNER TO postgres;

--
-- Name: files_folder_lnk_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.files_folder_lnk_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.files_folder_lnk_id_seq OWNER TO postgres;

--
-- Name: files_folder_lnk_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.files_folder_lnk_id_seq OWNED BY public.files_folder_lnk.id;


--
-- Name: files_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.files_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.files_id_seq OWNER TO postgres;

--
-- Name: files_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.files_id_seq OWNED BY public.files.id;


--
-- Name: files_related_mph; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.files_related_mph (
    id integer NOT NULL,
    file_id integer,
    related_id integer,
    related_type character varying(255),
    field character varying(255),
    "order" double precision
);


ALTER TABLE public.files_related_mph OWNER TO postgres;

--
-- Name: files_related_mph_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.files_related_mph_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.files_related_mph_id_seq OWNER TO postgres;

--
-- Name: files_related_mph_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.files_related_mph_id_seq OWNED BY public.files_related_mph.id;


--
-- Name: i18n_locale; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.i18n_locale (
    id integer NOT NULL,
    document_id character varying(255),
    name character varying(255),
    code character varying(255),
    created_at timestamp(6) without time zone,
    updated_at timestamp(6) without time zone,
    published_at timestamp(6) without time zone,
    created_by_id integer,
    updated_by_id integer,
    locale character varying(255)
);


ALTER TABLE public.i18n_locale OWNER TO postgres;

--
-- Name: i18n_locale_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.i18n_locale_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.i18n_locale_id_seq OWNER TO postgres;

--
-- Name: i18n_locale_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.i18n_locale_id_seq OWNED BY public.i18n_locale.id;


--
-- Name: paper_infos; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.paper_infos (
    id integer NOT NULL,
    document_id character varying(255),
    doi character varying(255),
    title character varying(255),
    authors text,
    year integer,
    journal character varying(255),
    abstract text,
    bibtex text,
    created_at timestamp(6) without time zone,
    updated_at timestamp(6) without time zone,
    published_at timestamp(6) without time zone,
    created_by_id integer,
    updated_by_id integer,
    locale character varying(255)
);


ALTER TABLE public.paper_infos OWNER TO postgres;

--
-- Name: paper_infos_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.paper_infos_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.paper_infos_id_seq OWNER TO postgres;

--
-- Name: paper_infos_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.paper_infos_id_seq OWNED BY public.paper_infos.id;


--
-- Name: strapi_api_token_permissions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.strapi_api_token_permissions (
    id integer NOT NULL,
    document_id character varying(255),
    action character varying(255),
    created_at timestamp(6) without time zone,
    updated_at timestamp(6) without time zone,
    published_at timestamp(6) without time zone,
    created_by_id integer,
    updated_by_id integer,
    locale character varying(255)
);


ALTER TABLE public.strapi_api_token_permissions OWNER TO postgres;

--
-- Name: strapi_api_token_permissions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.strapi_api_token_permissions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.strapi_api_token_permissions_id_seq OWNER TO postgres;

--
-- Name: strapi_api_token_permissions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.strapi_api_token_permissions_id_seq OWNED BY public.strapi_api_token_permissions.id;


--
-- Name: strapi_api_token_permissions_token_lnk; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.strapi_api_token_permissions_token_lnk (
    id integer NOT NULL,
    api_token_permission_id integer,
    api_token_id integer,
    api_token_permission_ord double precision
);


ALTER TABLE public.strapi_api_token_permissions_token_lnk OWNER TO postgres;

--
-- Name: strapi_api_token_permissions_token_lnk_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.strapi_api_token_permissions_token_lnk_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.strapi_api_token_permissions_token_lnk_id_seq OWNER TO postgres;

--
-- Name: strapi_api_token_permissions_token_lnk_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.strapi_api_token_permissions_token_lnk_id_seq OWNED BY public.strapi_api_token_permissions_token_lnk.id;


--
-- Name: strapi_api_tokens; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.strapi_api_tokens (
    id integer NOT NULL,
    document_id character varying(255),
    name character varying(255),
    description character varying(255),
    type character varying(255),
    access_key character varying(255),
    encrypted_key text,
    last_used_at timestamp(6) without time zone,
    expires_at timestamp(6) without time zone,
    lifespan bigint,
    created_at timestamp(6) without time zone,
    updated_at timestamp(6) without time zone,
    published_at timestamp(6) without time zone,
    created_by_id integer,
    updated_by_id integer,
    locale character varying(255)
);


ALTER TABLE public.strapi_api_tokens OWNER TO postgres;

--
-- Name: strapi_api_tokens_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.strapi_api_tokens_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.strapi_api_tokens_id_seq OWNER TO postgres;

--
-- Name: strapi_api_tokens_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.strapi_api_tokens_id_seq OWNED BY public.strapi_api_tokens.id;


--
-- Name: strapi_core_store_settings; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.strapi_core_store_settings (
    id integer NOT NULL,
    key character varying(255),
    value text,
    type character varying(255),
    environment character varying(255),
    tag character varying(255)
);


ALTER TABLE public.strapi_core_store_settings OWNER TO postgres;

--
-- Name: strapi_core_store_settings_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.strapi_core_store_settings_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.strapi_core_store_settings_id_seq OWNER TO postgres;

--
-- Name: strapi_core_store_settings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.strapi_core_store_settings_id_seq OWNED BY public.strapi_core_store_settings.id;


--
-- Name: strapi_database_schema; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.strapi_database_schema (
    id integer NOT NULL,
    schema json,
    "time" timestamp without time zone,
    hash character varying(255)
);


ALTER TABLE public.strapi_database_schema OWNER TO postgres;

--
-- Name: strapi_database_schema_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.strapi_database_schema_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.strapi_database_schema_id_seq OWNER TO postgres;

--
-- Name: strapi_database_schema_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.strapi_database_schema_id_seq OWNED BY public.strapi_database_schema.id;


--
-- Name: strapi_history_versions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.strapi_history_versions (
    id integer NOT NULL,
    content_type character varying(255) NOT NULL,
    related_document_id character varying(255),
    locale character varying(255),
    status character varying(255),
    data jsonb,
    schema jsonb,
    created_at timestamp(6) without time zone,
    created_by_id integer
);


ALTER TABLE public.strapi_history_versions OWNER TO postgres;

--
-- Name: strapi_history_versions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.strapi_history_versions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.strapi_history_versions_id_seq OWNER TO postgres;

--
-- Name: strapi_history_versions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.strapi_history_versions_id_seq OWNED BY public.strapi_history_versions.id;


--
-- Name: strapi_migrations; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.strapi_migrations (
    id integer NOT NULL,
    name character varying(255),
    "time" timestamp without time zone
);


ALTER TABLE public.strapi_migrations OWNER TO postgres;

--
-- Name: strapi_migrations_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.strapi_migrations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.strapi_migrations_id_seq OWNER TO postgres;

--
-- Name: strapi_migrations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.strapi_migrations_id_seq OWNED BY public.strapi_migrations.id;


--
-- Name: strapi_migrations_internal; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.strapi_migrations_internal (
    id integer NOT NULL,
    name character varying(255),
    "time" timestamp without time zone
);


ALTER TABLE public.strapi_migrations_internal OWNER TO postgres;

--
-- Name: strapi_migrations_internal_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.strapi_migrations_internal_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.strapi_migrations_internal_id_seq OWNER TO postgres;

--
-- Name: strapi_migrations_internal_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.strapi_migrations_internal_id_seq OWNED BY public.strapi_migrations_internal.id;


--
-- Name: strapi_release_actions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.strapi_release_actions (
    id integer NOT NULL,
    document_id character varying(255),
    type character varying(255),
    content_type character varying(255),
    entry_document_id character varying(255),
    locale character varying(255),
    is_entry_valid boolean,
    created_at timestamp(6) without time zone,
    updated_at timestamp(6) without time zone,
    published_at timestamp(6) without time zone,
    created_by_id integer,
    updated_by_id integer
);


ALTER TABLE public.strapi_release_actions OWNER TO postgres;

--
-- Name: strapi_release_actions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.strapi_release_actions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.strapi_release_actions_id_seq OWNER TO postgres;

--
-- Name: strapi_release_actions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.strapi_release_actions_id_seq OWNED BY public.strapi_release_actions.id;


--
-- Name: strapi_release_actions_release_lnk; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.strapi_release_actions_release_lnk (
    id integer NOT NULL,
    release_action_id integer,
    release_id integer,
    release_action_ord double precision
);


ALTER TABLE public.strapi_release_actions_release_lnk OWNER TO postgres;

--
-- Name: strapi_release_actions_release_lnk_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.strapi_release_actions_release_lnk_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.strapi_release_actions_release_lnk_id_seq OWNER TO postgres;

--
-- Name: strapi_release_actions_release_lnk_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.strapi_release_actions_release_lnk_id_seq OWNED BY public.strapi_release_actions_release_lnk.id;


--
-- Name: strapi_releases; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.strapi_releases (
    id integer NOT NULL,
    document_id character varying(255),
    name character varying(255),
    released_at timestamp(6) without time zone,
    scheduled_at timestamp(6) without time zone,
    timezone character varying(255),
    status character varying(255),
    created_at timestamp(6) without time zone,
    updated_at timestamp(6) without time zone,
    published_at timestamp(6) without time zone,
    created_by_id integer,
    updated_by_id integer,
    locale character varying(255)
);


ALTER TABLE public.strapi_releases OWNER TO postgres;

--
-- Name: strapi_releases_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.strapi_releases_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.strapi_releases_id_seq OWNER TO postgres;

--
-- Name: strapi_releases_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.strapi_releases_id_seq OWNED BY public.strapi_releases.id;


--
-- Name: strapi_transfer_token_permissions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.strapi_transfer_token_permissions (
    id integer NOT NULL,
    document_id character varying(255),
    action character varying(255),
    created_at timestamp(6) without time zone,
    updated_at timestamp(6) without time zone,
    published_at timestamp(6) without time zone,
    created_by_id integer,
    updated_by_id integer,
    locale character varying(255)
);


ALTER TABLE public.strapi_transfer_token_permissions OWNER TO postgres;

--
-- Name: strapi_transfer_token_permissions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.strapi_transfer_token_permissions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.strapi_transfer_token_permissions_id_seq OWNER TO postgres;

--
-- Name: strapi_transfer_token_permissions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.strapi_transfer_token_permissions_id_seq OWNED BY public.strapi_transfer_token_permissions.id;


--
-- Name: strapi_transfer_token_permissions_token_lnk; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.strapi_transfer_token_permissions_token_lnk (
    id integer NOT NULL,
    transfer_token_permission_id integer,
    transfer_token_id integer,
    transfer_token_permission_ord double precision
);


ALTER TABLE public.strapi_transfer_token_permissions_token_lnk OWNER TO postgres;

--
-- Name: strapi_transfer_token_permissions_token_lnk_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.strapi_transfer_token_permissions_token_lnk_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.strapi_transfer_token_permissions_token_lnk_id_seq OWNER TO postgres;

--
-- Name: strapi_transfer_token_permissions_token_lnk_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.strapi_transfer_token_permissions_token_lnk_id_seq OWNED BY public.strapi_transfer_token_permissions_token_lnk.id;


--
-- Name: strapi_transfer_tokens; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.strapi_transfer_tokens (
    id integer NOT NULL,
    document_id character varying(255),
    name character varying(255),
    description character varying(255),
    access_key character varying(255),
    last_used_at timestamp(6) without time zone,
    expires_at timestamp(6) without time zone,
    lifespan bigint,
    created_at timestamp(6) without time zone,
    updated_at timestamp(6) without time zone,
    published_at timestamp(6) without time zone,
    created_by_id integer,
    updated_by_id integer,
    locale character varying(255)
);


ALTER TABLE public.strapi_transfer_tokens OWNER TO postgres;

--
-- Name: strapi_transfer_tokens_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.strapi_transfer_tokens_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.strapi_transfer_tokens_id_seq OWNER TO postgres;

--
-- Name: strapi_transfer_tokens_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.strapi_transfer_tokens_id_seq OWNED BY public.strapi_transfer_tokens.id;


--
-- Name: strapi_webhooks; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.strapi_webhooks (
    id integer NOT NULL,
    name character varying(255),
    url text,
    headers jsonb,
    events jsonb,
    enabled boolean
);


ALTER TABLE public.strapi_webhooks OWNER TO postgres;

--
-- Name: strapi_webhooks_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.strapi_webhooks_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.strapi_webhooks_id_seq OWNER TO postgres;

--
-- Name: strapi_webhooks_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.strapi_webhooks_id_seq OWNED BY public.strapi_webhooks.id;


--
-- Name: strapi_workflows; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.strapi_workflows (
    id integer NOT NULL,
    document_id character varying(255),
    name character varying(255),
    content_types jsonb,
    created_at timestamp(6) without time zone,
    updated_at timestamp(6) without time zone,
    published_at timestamp(6) without time zone,
    created_by_id integer,
    updated_by_id integer,
    locale character varying(255)
);


ALTER TABLE public.strapi_workflows OWNER TO postgres;

--
-- Name: strapi_workflows_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.strapi_workflows_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.strapi_workflows_id_seq OWNER TO postgres;

--
-- Name: strapi_workflows_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.strapi_workflows_id_seq OWNED BY public.strapi_workflows.id;


--
-- Name: strapi_workflows_stage_required_to_publish_lnk; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.strapi_workflows_stage_required_to_publish_lnk (
    id integer NOT NULL,
    workflow_id integer,
    workflow_stage_id integer
);


ALTER TABLE public.strapi_workflows_stage_required_to_publish_lnk OWNER TO postgres;

--
-- Name: strapi_workflows_stage_required_to_publish_lnk_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.strapi_workflows_stage_required_to_publish_lnk_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.strapi_workflows_stage_required_to_publish_lnk_id_seq OWNER TO postgres;

--
-- Name: strapi_workflows_stage_required_to_publish_lnk_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.strapi_workflows_stage_required_to_publish_lnk_id_seq OWNED BY public.strapi_workflows_stage_required_to_publish_lnk.id;


--
-- Name: strapi_workflows_stages; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.strapi_workflows_stages (
    id integer NOT NULL,
    document_id character varying(255),
    name character varying(255),
    color character varying(255),
    created_at timestamp(6) without time zone,
    updated_at timestamp(6) without time zone,
    published_at timestamp(6) without time zone,
    created_by_id integer,
    updated_by_id integer,
    locale character varying(255)
);


ALTER TABLE public.strapi_workflows_stages OWNER TO postgres;

--
-- Name: strapi_workflows_stages_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.strapi_workflows_stages_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.strapi_workflows_stages_id_seq OWNER TO postgres;

--
-- Name: strapi_workflows_stages_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.strapi_workflows_stages_id_seq OWNED BY public.strapi_workflows_stages.id;


--
-- Name: strapi_workflows_stages_permissions_lnk; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.strapi_workflows_stages_permissions_lnk (
    id integer NOT NULL,
    workflow_stage_id integer,
    permission_id integer,
    permission_ord double precision
);


ALTER TABLE public.strapi_workflows_stages_permissions_lnk OWNER TO postgres;

--
-- Name: strapi_workflows_stages_permissions_lnk_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.strapi_workflows_stages_permissions_lnk_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.strapi_workflows_stages_permissions_lnk_id_seq OWNER TO postgres;

--
-- Name: strapi_workflows_stages_permissions_lnk_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.strapi_workflows_stages_permissions_lnk_id_seq OWNED BY public.strapi_workflows_stages_permissions_lnk.id;


--
-- Name: strapi_workflows_stages_workflow_lnk; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.strapi_workflows_stages_workflow_lnk (
    id integer NOT NULL,
    workflow_stage_id integer,
    workflow_id integer,
    workflow_stage_ord double precision
);


ALTER TABLE public.strapi_workflows_stages_workflow_lnk OWNER TO postgres;

--
-- Name: strapi_workflows_stages_workflow_lnk_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.strapi_workflows_stages_workflow_lnk_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.strapi_workflows_stages_workflow_lnk_id_seq OWNER TO postgres;

--
-- Name: strapi_workflows_stages_workflow_lnk_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.strapi_workflows_stages_workflow_lnk_id_seq OWNED BY public.strapi_workflows_stages_workflow_lnk.id;


--
-- Name: team_members; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.team_members (
    id integer NOT NULL,
    document_id character varying(255),
    name character varying(255),
    title character varying(255),
    role character varying(255),
    email character varying(255),
    phone character varying(255),
    website character varying(255),
    bio jsonb,
    enrollment_year integer,
    graduation_year integer,
    company character varying(255),
    "position" character varying(255),
    sort_order integer,
    created_at timestamp(6) without time zone,
    updated_at timestamp(6) without time zone,
    published_at timestamp(6) without time zone,
    created_by_id integer,
    updated_by_id integer,
    locale character varying(255),
    education jsonb,
    research_direction jsonb
);


ALTER TABLE public.team_members OWNER TO postgres;

--
-- Name: team_members_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.team_members_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.team_members_id_seq OWNER TO postgres;

--
-- Name: team_members_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.team_members_id_seq OWNED BY public.team_members.id;


--
-- Name: up_permissions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.up_permissions (
    id integer NOT NULL,
    document_id character varying(255),
    action character varying(255),
    created_at timestamp(6) without time zone,
    updated_at timestamp(6) without time zone,
    published_at timestamp(6) without time zone,
    created_by_id integer,
    updated_by_id integer,
    locale character varying(255)
);


ALTER TABLE public.up_permissions OWNER TO postgres;

--
-- Name: up_permissions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.up_permissions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.up_permissions_id_seq OWNER TO postgres;

--
-- Name: up_permissions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.up_permissions_id_seq OWNED BY public.up_permissions.id;


--
-- Name: up_permissions_role_lnk; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.up_permissions_role_lnk (
    id integer NOT NULL,
    permission_id integer,
    role_id integer,
    permission_ord double precision
);


ALTER TABLE public.up_permissions_role_lnk OWNER TO postgres;

--
-- Name: up_permissions_role_lnk_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.up_permissions_role_lnk_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.up_permissions_role_lnk_id_seq OWNER TO postgres;

--
-- Name: up_permissions_role_lnk_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.up_permissions_role_lnk_id_seq OWNED BY public.up_permissions_role_lnk.id;


--
-- Name: up_roles; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.up_roles (
    id integer NOT NULL,
    document_id character varying(255),
    name character varying(255),
    description character varying(255),
    type character varying(255),
    created_at timestamp(6) without time zone,
    updated_at timestamp(6) without time zone,
    published_at timestamp(6) without time zone,
    created_by_id integer,
    updated_by_id integer,
    locale character varying(255)
);


ALTER TABLE public.up_roles OWNER TO postgres;

--
-- Name: up_roles_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.up_roles_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.up_roles_id_seq OWNER TO postgres;

--
-- Name: up_roles_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.up_roles_id_seq OWNED BY public.up_roles.id;


--
-- Name: up_users; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.up_users (
    id integer NOT NULL,
    document_id character varying(255),
    username character varying(255),
    email character varying(255),
    provider character varying(255),
    password character varying(255),
    reset_password_token character varying(255),
    confirmation_token character varying(255),
    confirmed boolean,
    blocked boolean,
    created_at timestamp(6) without time zone,
    updated_at timestamp(6) without time zone,
    published_at timestamp(6) without time zone,
    created_by_id integer,
    updated_by_id integer,
    locale character varying(255)
);


ALTER TABLE public.up_users OWNER TO postgres;

--
-- Name: up_users_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.up_users_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.up_users_id_seq OWNER TO postgres;

--
-- Name: up_users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.up_users_id_seq OWNED BY public.up_users.id;


--
-- Name: up_users_role_lnk; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.up_users_role_lnk (
    id integer NOT NULL,
    user_id integer,
    role_id integer,
    user_ord double precision
);


ALTER TABLE public.up_users_role_lnk OWNER TO postgres;

--
-- Name: up_users_role_lnk_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.up_users_role_lnk_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.up_users_role_lnk_id_seq OWNER TO postgres;

--
-- Name: up_users_role_lnk_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.up_users_role_lnk_id_seq OWNED BY public.up_users_role_lnk.id;


--
-- Name: upload_folders; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.upload_folders (
    id integer NOT NULL,
    document_id character varying(255),
    name character varying(255),
    path_id integer,
    path character varying(255),
    created_at timestamp(6) without time zone,
    updated_at timestamp(6) without time zone,
    published_at timestamp(6) without time zone,
    created_by_id integer,
    updated_by_id integer,
    locale character varying(255)
);


ALTER TABLE public.upload_folders OWNER TO postgres;

--
-- Name: upload_folders_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.upload_folders_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.upload_folders_id_seq OWNER TO postgres;

--
-- Name: upload_folders_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.upload_folders_id_seq OWNED BY public.upload_folders.id;


--
-- Name: upload_folders_parent_lnk; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.upload_folders_parent_lnk (
    id integer NOT NULL,
    folder_id integer,
    inv_folder_id integer,
    folder_ord double precision
);


ALTER TABLE public.upload_folders_parent_lnk OWNER TO postgres;

--
-- Name: upload_folders_parent_lnk_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.upload_folders_parent_lnk_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.upload_folders_parent_lnk_id_seq OWNER TO postgres;

--
-- Name: upload_folders_parent_lnk_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.upload_folders_parent_lnk_id_seq OWNED BY public.upload_folders_parent_lnk.id;


--
-- Name: admin_permissions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.admin_permissions ALTER COLUMN id SET DEFAULT nextval('public.admin_permissions_id_seq'::regclass);


--
-- Name: admin_permissions_role_lnk id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.admin_permissions_role_lnk ALTER COLUMN id SET DEFAULT nextval('public.admin_permissions_role_lnk_id_seq'::regclass);


--
-- Name: admin_roles id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.admin_roles ALTER COLUMN id SET DEFAULT nextval('public.admin_roles_id_seq'::regclass);


--
-- Name: admin_users id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.admin_users ALTER COLUMN id SET DEFAULT nextval('public.admin_users_id_seq'::regclass);


--
-- Name: admin_users_roles_lnk id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.admin_users_roles_lnk ALTER COLUMN id SET DEFAULT nextval('public.admin_users_roles_lnk_id_seq'::regclass);


--
-- Name: doi_collections id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.doi_collections ALTER COLUMN id SET DEFAULT nextval('public.doi_collections_id_seq'::regclass);


--
-- Name: files id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.files ALTER COLUMN id SET DEFAULT nextval('public.files_id_seq'::regclass);


--
-- Name: files_folder_lnk id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.files_folder_lnk ALTER COLUMN id SET DEFAULT nextval('public.files_folder_lnk_id_seq'::regclass);


--
-- Name: files_related_mph id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.files_related_mph ALTER COLUMN id SET DEFAULT nextval('public.files_related_mph_id_seq'::regclass);


--
-- Name: i18n_locale id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.i18n_locale ALTER COLUMN id SET DEFAULT nextval('public.i18n_locale_id_seq'::regclass);


--
-- Name: paper_infos id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.paper_infos ALTER COLUMN id SET DEFAULT nextval('public.paper_infos_id_seq'::regclass);


--
-- Name: strapi_api_token_permissions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_api_token_permissions ALTER COLUMN id SET DEFAULT nextval('public.strapi_api_token_permissions_id_seq'::regclass);


--
-- Name: strapi_api_token_permissions_token_lnk id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_api_token_permissions_token_lnk ALTER COLUMN id SET DEFAULT nextval('public.strapi_api_token_permissions_token_lnk_id_seq'::regclass);


--
-- Name: strapi_api_tokens id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_api_tokens ALTER COLUMN id SET DEFAULT nextval('public.strapi_api_tokens_id_seq'::regclass);


--
-- Name: strapi_core_store_settings id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_core_store_settings ALTER COLUMN id SET DEFAULT nextval('public.strapi_core_store_settings_id_seq'::regclass);


--
-- Name: strapi_database_schema id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_database_schema ALTER COLUMN id SET DEFAULT nextval('public.strapi_database_schema_id_seq'::regclass);


--
-- Name: strapi_history_versions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_history_versions ALTER COLUMN id SET DEFAULT nextval('public.strapi_history_versions_id_seq'::regclass);


--
-- Name: strapi_migrations id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_migrations ALTER COLUMN id SET DEFAULT nextval('public.strapi_migrations_id_seq'::regclass);


--
-- Name: strapi_migrations_internal id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_migrations_internal ALTER COLUMN id SET DEFAULT nextval('public.strapi_migrations_internal_id_seq'::regclass);


--
-- Name: strapi_release_actions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_release_actions ALTER COLUMN id SET DEFAULT nextval('public.strapi_release_actions_id_seq'::regclass);


--
-- Name: strapi_release_actions_release_lnk id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_release_actions_release_lnk ALTER COLUMN id SET DEFAULT nextval('public.strapi_release_actions_release_lnk_id_seq'::regclass);


--
-- Name: strapi_releases id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_releases ALTER COLUMN id SET DEFAULT nextval('public.strapi_releases_id_seq'::regclass);


--
-- Name: strapi_transfer_token_permissions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_transfer_token_permissions ALTER COLUMN id SET DEFAULT nextval('public.strapi_transfer_token_permissions_id_seq'::regclass);


--
-- Name: strapi_transfer_token_permissions_token_lnk id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_transfer_token_permissions_token_lnk ALTER COLUMN id SET DEFAULT nextval('public.strapi_transfer_token_permissions_token_lnk_id_seq'::regclass);


--
-- Name: strapi_transfer_tokens id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_transfer_tokens ALTER COLUMN id SET DEFAULT nextval('public.strapi_transfer_tokens_id_seq'::regclass);


--
-- Name: strapi_webhooks id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_webhooks ALTER COLUMN id SET DEFAULT nextval('public.strapi_webhooks_id_seq'::regclass);


--
-- Name: strapi_workflows id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_workflows ALTER COLUMN id SET DEFAULT nextval('public.strapi_workflows_id_seq'::regclass);


--
-- Name: strapi_workflows_stage_required_to_publish_lnk id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_workflows_stage_required_to_publish_lnk ALTER COLUMN id SET DEFAULT nextval('public.strapi_workflows_stage_required_to_publish_lnk_id_seq'::regclass);


--
-- Name: strapi_workflows_stages id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_workflows_stages ALTER COLUMN id SET DEFAULT nextval('public.strapi_workflows_stages_id_seq'::regclass);


--
-- Name: strapi_workflows_stages_permissions_lnk id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_workflows_stages_permissions_lnk ALTER COLUMN id SET DEFAULT nextval('public.strapi_workflows_stages_permissions_lnk_id_seq'::regclass);


--
-- Name: strapi_workflows_stages_workflow_lnk id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_workflows_stages_workflow_lnk ALTER COLUMN id SET DEFAULT nextval('public.strapi_workflows_stages_workflow_lnk_id_seq'::regclass);


--
-- Name: team_members id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.team_members ALTER COLUMN id SET DEFAULT nextval('public.team_members_id_seq'::regclass);


--
-- Name: up_permissions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.up_permissions ALTER COLUMN id SET DEFAULT nextval('public.up_permissions_id_seq'::regclass);


--
-- Name: up_permissions_role_lnk id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.up_permissions_role_lnk ALTER COLUMN id SET DEFAULT nextval('public.up_permissions_role_lnk_id_seq'::regclass);


--
-- Name: up_roles id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.up_roles ALTER COLUMN id SET DEFAULT nextval('public.up_roles_id_seq'::regclass);


--
-- Name: up_users id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.up_users ALTER COLUMN id SET DEFAULT nextval('public.up_users_id_seq'::regclass);


--
-- Name: up_users_role_lnk id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.up_users_role_lnk ALTER COLUMN id SET DEFAULT nextval('public.up_users_role_lnk_id_seq'::regclass);


--
-- Name: upload_folders id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.upload_folders ALTER COLUMN id SET DEFAULT nextval('public.upload_folders_id_seq'::regclass);


--
-- Name: upload_folders_parent_lnk id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.upload_folders_parent_lnk ALTER COLUMN id SET DEFAULT nextval('public.upload_folders_parent_lnk_id_seq'::regclass);


--
-- Data for Name: admin_permissions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.admin_permissions (id, document_id, action, action_parameters, subject, properties, conditions, created_at, updated_at, published_at, created_by_id, updated_by_id, locale) FROM stdin;
1	l4d8efnrser601qnuelkmjfz	plugin::upload.read	{}	\N	{}	[]	2025-07-20 15:48:25.963	2025-07-20 15:48:25.963	2025-07-20 15:48:25.963	\N	\N	\N
2	qhykimbf1sugh3n1if4bhjda	plugin::upload.configure-view	{}	\N	{}	[]	2025-07-20 15:48:25.971	2025-07-20 15:48:25.971	2025-07-20 15:48:25.971	\N	\N	\N
3	j8f5iqvu7900mgv760iw0i7c	plugin::upload.assets.create	{}	\N	{}	[]	2025-07-20 15:48:25.978	2025-07-20 15:48:25.978	2025-07-20 15:48:25.978	\N	\N	\N
4	cepo32bpsczplrvg9tm0kkqt	plugin::upload.assets.update	{}	\N	{}	[]	2025-07-20 15:48:25.985	2025-07-20 15:48:25.985	2025-07-20 15:48:25.985	\N	\N	\N
5	vkedqca7xfxj4693vpbs22ph	plugin::upload.assets.download	{}	\N	{}	[]	2025-07-20 15:48:25.995	2025-07-20 15:48:25.995	2025-07-20 15:48:25.995	\N	\N	\N
6	b4v56ref0jxqqcadtqa06nyw	plugin::upload.assets.copy-link	{}	\N	{}	[]	2025-07-20 15:48:26.002	2025-07-20 15:48:26.002	2025-07-20 15:48:26.002	\N	\N	\N
7	ijs7pumtlmm1b2s0vqmpgd7r	plugin::upload.read	{}	\N	{}	["admin::is-creator"]	2025-07-20 15:48:26.01	2025-07-20 15:48:26.01	2025-07-20 15:48:26.01	\N	\N	\N
8	vtf3rtqoan151l6fef71z9uj	plugin::upload.configure-view	{}	\N	{}	[]	2025-07-20 15:48:26.021	2025-07-20 15:48:26.021	2025-07-20 15:48:26.021	\N	\N	\N
9	iuarjy3fhbmwysc3mr9vbcrw	plugin::upload.assets.create	{}	\N	{}	[]	2025-07-20 15:48:26.031	2025-07-20 15:48:26.031	2025-07-20 15:48:26.031	\N	\N	\N
10	ruw6xnilja4o40wjzge6m0qr	plugin::upload.assets.update	{}	\N	{}	["admin::is-creator"]	2025-07-20 15:48:26.039	2025-07-20 15:48:26.039	2025-07-20 15:48:26.04	\N	\N	\N
11	s2u066qgtwgmdgwerxdx5jil	plugin::upload.assets.download	{}	\N	{}	[]	2025-07-20 15:48:26.046	2025-07-20 15:48:26.046	2025-07-20 15:48:26.046	\N	\N	\N
12	duu9askb2vgpbsfzlahh40bs	plugin::upload.assets.copy-link	{}	\N	{}	[]	2025-07-20 15:48:26.052	2025-07-20 15:48:26.052	2025-07-20 15:48:26.052	\N	\N	\N
13	z8cqof38cark1uvyl46b0bii	plugin::content-manager.explorer.create	{}	plugin::users-permissions.user	{"fields": ["username", "email", "provider", "password", "resetPasswordToken", "confirmationToken", "confirmed", "blocked", "role"]}	[]	2025-07-20 15:48:26.072	2025-07-20 15:48:26.072	2025-07-20 15:48:26.072	\N	\N	\N
14	c3fu7ol6sevsnwot4pp34918	plugin::content-manager.explorer.read	{}	plugin::users-permissions.user	{"fields": ["username", "email", "provider", "password", "resetPasswordToken", "confirmationToken", "confirmed", "blocked", "role"]}	[]	2025-07-20 15:48:26.08	2025-07-20 15:48:26.08	2025-07-20 15:48:26.081	\N	\N	\N
15	mqhned3debmcnkpfkrkm61gk	plugin::content-manager.explorer.update	{}	plugin::users-permissions.user	{"fields": ["username", "email", "provider", "password", "resetPasswordToken", "confirmationToken", "confirmed", "blocked", "role"]}	[]	2025-07-20 15:48:26.087	2025-07-20 15:48:26.087	2025-07-20 15:48:26.087	\N	\N	\N
16	al18fvy29pkxwc06jyiu1zhx	plugin::content-manager.explorer.delete	{}	plugin::users-permissions.user	{}	[]	2025-07-20 15:48:26.095	2025-07-20 15:48:26.095	2025-07-20 15:48:26.095	\N	\N	\N
17	tacen4g7ng6yvh3aad2i93jm	plugin::content-manager.explorer.publish	{}	plugin::users-permissions.user	{}	[]	2025-07-20 15:48:26.101	2025-07-20 15:48:26.101	2025-07-20 15:48:26.101	\N	\N	\N
18	w2o4lonk1d0faia2na5hu4g2	plugin::content-manager.single-types.configure-view	{}	\N	{}	[]	2025-07-20 15:48:26.107	2025-07-20 15:48:26.107	2025-07-20 15:48:26.107	\N	\N	\N
19	eha1dk645gcarf150q5eqrtf	plugin::content-manager.collection-types.configure-view	{}	\N	{}	[]	2025-07-20 15:48:26.112	2025-07-20 15:48:26.112	2025-07-20 15:48:26.112	\N	\N	\N
20	ae8eht8d4rjpzeqwgnio2e59	plugin::content-manager.components.configure-layout	{}	\N	{}	[]	2025-07-20 15:48:26.118	2025-07-20 15:48:26.118	2025-07-20 15:48:26.118	\N	\N	\N
21	phqbml412qt2tmm57rwfhgu9	plugin::content-type-builder.read	{}	\N	{}	[]	2025-07-20 15:48:26.126	2025-07-20 15:48:26.126	2025-07-20 15:48:26.126	\N	\N	\N
22	gbpvmkwk3yimk9fix003q4de	plugin::email.settings.read	{}	\N	{}	[]	2025-07-20 15:48:26.133	2025-07-20 15:48:26.133	2025-07-20 15:48:26.134	\N	\N	\N
23	f2lqvwrk27kmn1f1ug86abja	plugin::upload.read	{}	\N	{}	[]	2025-07-20 15:48:26.144	2025-07-20 15:48:26.144	2025-07-20 15:48:26.145	\N	\N	\N
24	fv9xoozre9tu3zb151n981lx	plugin::upload.assets.create	{}	\N	{}	[]	2025-07-20 15:48:26.152	2025-07-20 15:48:26.152	2025-07-20 15:48:26.152	\N	\N	\N
25	aak2td4xh8zhby4cw8ezjuev	plugin::upload.assets.update	{}	\N	{}	[]	2025-07-20 15:48:26.164	2025-07-20 15:48:26.164	2025-07-20 15:48:26.164	\N	\N	\N
26	rlkgrrpoqfz79um75eonotmh	plugin::upload.assets.download	{}	\N	{}	[]	2025-07-20 15:48:26.17	2025-07-20 15:48:26.17	2025-07-20 15:48:26.17	\N	\N	\N
27	psm2mp8dwz2rkghdif1npuor	plugin::upload.assets.copy-link	{}	\N	{}	[]	2025-07-20 15:48:26.177	2025-07-20 15:48:26.177	2025-07-20 15:48:26.177	\N	\N	\N
28	sn8ndwr34k3mac5takj5yut4	plugin::upload.configure-view	{}	\N	{}	[]	2025-07-20 15:48:26.183	2025-07-20 15:48:26.183	2025-07-20 15:48:26.183	\N	\N	\N
29	aj8bbzc5ypow1o8pu6xt8adj	plugin::upload.settings.read	{}	\N	{}	[]	2025-07-20 15:48:26.189	2025-07-20 15:48:26.189	2025-07-20 15:48:26.189	\N	\N	\N
30	z0ijnmpbz8t18cw5f5ut75ff	plugin::i18n.locale.create	{}	\N	{}	[]	2025-07-20 15:48:26.195	2025-07-20 15:48:26.195	2025-07-20 15:48:26.195	\N	\N	\N
31	p4bgehwm95srcvyc8qxlas51	plugin::i18n.locale.read	{}	\N	{}	[]	2025-07-20 15:48:26.202	2025-07-20 15:48:26.202	2025-07-20 15:48:26.202	\N	\N	\N
32	usfvjxgaheehcswbzz12xoar	plugin::i18n.locale.update	{}	\N	{}	[]	2025-07-20 15:48:26.208	2025-07-20 15:48:26.208	2025-07-20 15:48:26.208	\N	\N	\N
33	t7jr332e504zx9sjd76j9y2y	plugin::i18n.locale.delete	{}	\N	{}	[]	2025-07-20 15:48:26.214	2025-07-20 15:48:26.214	2025-07-20 15:48:26.214	\N	\N	\N
34	om84eg01mfviw1zbkxccqqom	plugin::users-permissions.roles.create	{}	\N	{}	[]	2025-07-20 15:48:26.22	2025-07-20 15:48:26.22	2025-07-20 15:48:26.22	\N	\N	\N
35	i2b2p61z6uz2lxv518zqq0rf	plugin::users-permissions.roles.read	{}	\N	{}	[]	2025-07-20 15:48:26.23	2025-07-20 15:48:26.23	2025-07-20 15:48:26.23	\N	\N	\N
36	eqwsgfejc4k9a9hk4stt70lp	plugin::users-permissions.roles.update	{}	\N	{}	[]	2025-07-20 15:48:26.236	2025-07-20 15:48:26.236	2025-07-20 15:48:26.236	\N	\N	\N
37	o0gcdmkh1jw8x54vy1j03f0r	plugin::users-permissions.roles.delete	{}	\N	{}	[]	2025-07-20 15:48:26.243	2025-07-20 15:48:26.243	2025-07-20 15:48:26.243	\N	\N	\N
38	bpt2iu5e0zktk3ejk8d4tuyg	plugin::users-permissions.providers.read	{}	\N	{}	[]	2025-07-20 15:48:26.248	2025-07-20 15:48:26.248	2025-07-20 15:48:26.248	\N	\N	\N
39	s2nbujuo92gtr2fhb3s46k3x	plugin::users-permissions.providers.update	{}	\N	{}	[]	2025-07-20 15:48:26.253	2025-07-20 15:48:26.253	2025-07-20 15:48:26.253	\N	\N	\N
40	xakv5jsgr48i3zsbvqvxlh7e	plugin::users-permissions.email-templates.read	{}	\N	{}	[]	2025-07-20 15:48:26.26	2025-07-20 15:48:26.26	2025-07-20 15:48:26.26	\N	\N	\N
41	xwgniigbx2l66ucymqhcvgx6	plugin::users-permissions.email-templates.update	{}	\N	{}	[]	2025-07-20 15:48:26.266	2025-07-20 15:48:26.266	2025-07-20 15:48:26.266	\N	\N	\N
42	x5wlafxh6m5o00gidf69buyc	plugin::users-permissions.advanced-settings.read	{}	\N	{}	[]	2025-07-20 15:48:26.271	2025-07-20 15:48:26.271	2025-07-20 15:48:26.271	\N	\N	\N
43	fhre1iyi23fwrwzzjqh5acse	plugin::users-permissions.advanced-settings.update	{}	\N	{}	[]	2025-07-20 15:48:26.278	2025-07-20 15:48:26.278	2025-07-20 15:48:26.279	\N	\N	\N
44	dn4kv5k7vlsp3nhtz2781phj	admin::marketplace.read	{}	\N	{}	[]	2025-07-20 15:48:26.284	2025-07-20 15:48:26.284	2025-07-20 15:48:26.285	\N	\N	\N
45	slp94732j38mvc6f16z05772	admin::webhooks.create	{}	\N	{}	[]	2025-07-20 15:48:26.293	2025-07-20 15:48:26.293	2025-07-20 15:48:26.293	\N	\N	\N
46	ul3ybc48m7nn55gxhdern9a3	admin::webhooks.read	{}	\N	{}	[]	2025-07-20 15:48:26.299	2025-07-20 15:48:26.299	2025-07-20 15:48:26.299	\N	\N	\N
47	sdu18frykcm178h07gnxbzvt	admin::webhooks.update	{}	\N	{}	[]	2025-07-20 15:48:26.305	2025-07-20 15:48:26.305	2025-07-20 15:48:26.305	\N	\N	\N
48	gx03869yo0e38seuep4wplry	admin::webhooks.delete	{}	\N	{}	[]	2025-07-20 15:48:26.312	2025-07-20 15:48:26.312	2025-07-20 15:48:26.312	\N	\N	\N
49	yo2angh5sp7xvpmyq1hqx7g3	admin::users.create	{}	\N	{}	[]	2025-07-20 15:48:26.324	2025-07-20 15:48:26.324	2025-07-20 15:48:26.324	\N	\N	\N
50	xyggk8qhvauw0xus8ge69vvi	admin::users.read	{}	\N	{}	[]	2025-07-20 15:48:26.332	2025-07-20 15:48:26.332	2025-07-20 15:48:26.332	\N	\N	\N
51	lgb0gbq2jdb6s1pmcc904ecd	admin::users.update	{}	\N	{}	[]	2025-07-20 15:48:26.337	2025-07-20 15:48:26.337	2025-07-20 15:48:26.337	\N	\N	\N
52	qbvv66rd3gba2g30gb3cmd0m	admin::users.delete	{}	\N	{}	[]	2025-07-20 15:48:26.345	2025-07-20 15:48:26.345	2025-07-20 15:48:26.345	\N	\N	\N
53	p6noibd76k116v3kb30kt8gw	admin::roles.create	{}	\N	{}	[]	2025-07-20 15:48:26.35	2025-07-20 15:48:26.35	2025-07-20 15:48:26.35	\N	\N	\N
54	ncnewazrnxjmvxxmxscjlf01	admin::roles.read	{}	\N	{}	[]	2025-07-20 15:48:26.358	2025-07-20 15:48:26.358	2025-07-20 15:48:26.359	\N	\N	\N
55	a4w85v11dhikmh40clafs3fh	admin::roles.update	{}	\N	{}	[]	2025-07-20 15:48:26.364	2025-07-20 15:48:26.364	2025-07-20 15:48:26.364	\N	\N	\N
56	vzziuc0tb72e7okyy9wfyhha	admin::roles.delete	{}	\N	{}	[]	2025-07-20 15:48:26.371	2025-07-20 15:48:26.371	2025-07-20 15:48:26.371	\N	\N	\N
57	h2ztox1znmj89yc0lkzp46qt	admin::api-tokens.access	{}	\N	{}	[]	2025-07-20 15:48:26.377	2025-07-20 15:48:26.377	2025-07-20 15:48:26.377	\N	\N	\N
58	wj3jm9hwtpehegdp337byrdx	admin::api-tokens.create	{}	\N	{}	[]	2025-07-20 15:48:26.383	2025-07-20 15:48:26.383	2025-07-20 15:48:26.383	\N	\N	\N
59	vedkpmn6qyponggvu7vk2wxd	admin::api-tokens.read	{}	\N	{}	[]	2025-07-20 15:48:26.392	2025-07-20 15:48:26.392	2025-07-20 15:48:26.392	\N	\N	\N
60	ltxdhhb09ayq6xhm1jkaqy2v	admin::api-tokens.update	{}	\N	{}	[]	2025-07-20 15:48:26.399	2025-07-20 15:48:26.399	2025-07-20 15:48:26.399	\N	\N	\N
61	clpd5lq6uwdomkvty2cvyshe	admin::api-tokens.regenerate	{}	\N	{}	[]	2025-07-20 15:48:26.405	2025-07-20 15:48:26.405	2025-07-20 15:48:26.405	\N	\N	\N
62	un6z97q8yi20gifikb36pkfx	admin::api-tokens.delete	{}	\N	{}	[]	2025-07-20 15:48:26.412	2025-07-20 15:48:26.412	2025-07-20 15:48:26.412	\N	\N	\N
63	hik9rnc0eq3pwqxwzmjaxh51	admin::project-settings.update	{}	\N	{}	[]	2025-07-20 15:48:26.418	2025-07-20 15:48:26.418	2025-07-20 15:48:26.418	\N	\N	\N
64	va2e7w7b4f5b1v8zawnn2his	admin::project-settings.read	{}	\N	{}	[]	2025-07-20 15:48:26.427	2025-07-20 15:48:26.427	2025-07-20 15:48:26.427	\N	\N	\N
65	d780784n0a4f5hb97vxk5xfy	admin::transfer.tokens.access	{}	\N	{}	[]	2025-07-20 15:48:26.432	2025-07-20 15:48:26.432	2025-07-20 15:48:26.432	\N	\N	\N
66	sr3lzbmmzj4vz0dpmvscjnve	admin::transfer.tokens.create	{}	\N	{}	[]	2025-07-20 15:48:26.441	2025-07-20 15:48:26.441	2025-07-20 15:48:26.441	\N	\N	\N
67	xplyo0lq8bpap0wn6a7m6c8g	admin::transfer.tokens.read	{}	\N	{}	[]	2025-07-20 15:48:26.447	2025-07-20 15:48:26.447	2025-07-20 15:48:26.447	\N	\N	\N
68	bb805nm2ribdf8jmybnqfqj5	admin::transfer.tokens.update	{}	\N	{}	[]	2025-07-20 15:48:26.455	2025-07-20 15:48:26.455	2025-07-20 15:48:26.455	\N	\N	\N
69	tjldaue1bzxa5kc0gs1ibme2	admin::transfer.tokens.regenerate	{}	\N	{}	[]	2025-07-20 15:48:26.463	2025-07-20 15:48:26.463	2025-07-20 15:48:26.463	\N	\N	\N
70	jnbv6tgq54d7hs760ok8sl7f	admin::transfer.tokens.delete	{}	\N	{}	[]	2025-07-20 15:48:26.47	2025-07-20 15:48:26.47	2025-07-20 15:48:26.47	\N	\N	\N
74	gg3by7llb4a4yh2h4q462lrq	plugin::content-manager.explorer.delete	{}	api::team-member.team-member	{}	[]	2025-07-26 23:14:14.916	2025-07-26 23:14:14.916	2025-07-26 23:14:14.916	\N	\N	\N
75	gl9q3d8hxtc41bvlbsiw64gc	plugin::content-manager.explorer.publish	{}	api::team-member.team-member	{}	[]	2025-07-26 23:14:14.92	2025-07-26 23:14:14.92	2025-07-26 23:14:14.92	\N	\N	\N
88	vrmjzy1knls55vp8dkxjk1re	plugin::content-manager.explorer.create	{}	api::team-member.team-member	{"fields": ["name", "title", "role", "avatar", "researchDirection", "email", "phone", "website", "education", "bio", "enrollmentYear", "graduationYear", "company", "position", "sortOrder"]}	[]	2025-07-27 14:11:25.932	2025-07-27 14:11:25.932	2025-07-27 14:11:25.932	\N	\N	\N
89	l566yppkx0uhgq8rklr2opge	plugin::content-manager.explorer.read	{}	api::team-member.team-member	{"fields": ["name", "title", "role", "avatar", "researchDirection", "email", "phone", "website", "education", "bio", "enrollmentYear", "graduationYear", "company", "position", "sortOrder"]}	[]	2025-07-27 14:11:25.938	2025-07-27 14:11:25.938	2025-07-27 14:11:25.938	\N	\N	\N
90	r5dkfp4e6kwhev2mhvzfcrpr	plugin::content-manager.explorer.update	{}	api::team-member.team-member	{"fields": ["name", "title", "role", "avatar", "researchDirection", "email", "phone", "website", "education", "bio", "enrollmentYear", "graduationYear", "company", "position", "sortOrder"]}	[]	2025-07-27 14:11:25.941	2025-07-27 14:11:25.941	2025-07-27 14:11:25.942	\N	\N	\N
91	lcaeovqc03we13tv08jh1rwo	plugin::content-manager.explorer.create	{}	api::doi-collection.doi-collection	{"fields": ["doi"]}	[]	2025-07-28 22:59:09.455	2025-07-28 22:59:09.455	2025-07-28 22:59:09.456	\N	\N	\N
92	l2e6v4jfu9i6u8hqorx55d36	plugin::content-manager.explorer.read	{}	api::doi-collection.doi-collection	{"fields": ["doi"]}	[]	2025-07-28 22:59:09.462	2025-07-28 22:59:09.462	2025-07-28 22:59:09.462	\N	\N	\N
93	w4t7s0x4vl04i9g3pgvjgm42	plugin::content-manager.explorer.update	{}	api::doi-collection.doi-collection	{"fields": ["doi"]}	[]	2025-07-28 22:59:09.466	2025-07-28 22:59:09.466	2025-07-28 22:59:09.466	\N	\N	\N
94	mx5m4wi6h0vthj9hxgd1nzha	plugin::content-manager.explorer.delete	{}	api::doi-collection.doi-collection	{}	[]	2025-07-28 22:59:09.469	2025-07-28 22:59:09.469	2025-07-28 22:59:09.469	\N	\N	\N
95	nzj0duyvvxlfaesk9dyfea7v	plugin::content-manager.explorer.publish	{}	api::doi-collection.doi-collection	{}	[]	2025-07-28 22:59:09.473	2025-07-28 22:59:09.473	2025-07-28 22:59:09.473	\N	\N	\N
96	auasoa6b0g939si1ppmzss7b	plugin::content-manager.explorer.create	{}	api::paper-info.paper-info	{"fields": ["doi", "title", "authors", "year", "journal", "abstract", "bibtex"]}	[]	2025-07-28 23:33:50.4	2025-07-28 23:33:50.4	2025-07-28 23:33:50.401	\N	\N	\N
97	ua96r7meybogynd3vkd52np2	plugin::content-manager.explorer.read	{}	api::paper-info.paper-info	{"fields": ["doi", "title", "authors", "year", "journal", "abstract", "bibtex"]}	[]	2025-07-28 23:33:50.408	2025-07-28 23:33:50.408	2025-07-28 23:33:50.408	\N	\N	\N
98	yrqkhgq8twi8ypl08s8tkqly	plugin::content-manager.explorer.update	{}	api::paper-info.paper-info	{"fields": ["doi", "title", "authors", "year", "journal", "abstract", "bibtex"]}	[]	2025-07-28 23:33:50.412	2025-07-28 23:33:50.412	2025-07-28 23:33:50.413	\N	\N	\N
99	jrrea9be3q555em02ihu5n99	plugin::content-manager.explorer.delete	{}	api::paper-info.paper-info	{}	[]	2025-07-28 23:33:50.416	2025-07-28 23:33:50.416	2025-07-28 23:33:50.416	\N	\N	\N
100	ii8acztjkxhlp7yg825c10sa	plugin::content-manager.explorer.publish	{}	api::paper-info.paper-info	{}	[]	2025-07-28 23:33:50.42	2025-07-28 23:33:50.42	2025-07-28 23:33:50.42	\N	\N	\N
\.


--
-- Data for Name: admin_permissions_role_lnk; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.admin_permissions_role_lnk (id, permission_id, role_id, permission_ord) FROM stdin;
1	1	2	1
2	2	2	2
3	3	2	3
4	4	2	4
5	5	2	5
6	6	2	6
7	7	3	1
8	8	3	2
9	9	3	3
10	10	3	4
11	11	3	5
12	12	3	6
13	13	1	1
14	14	1	2
15	15	1	3
16	16	1	4
17	17	1	5
18	18	1	6
19	19	1	7
20	20	1	8
21	21	1	9
22	22	1	10
23	23	1	11
24	24	1	12
25	25	1	13
26	26	1	14
27	27	1	15
28	28	1	16
29	29	1	17
30	30	1	18
31	31	1	19
32	32	1	20
33	33	1	21
34	34	1	22
35	35	1	23
36	36	1	24
37	37	1	25
38	38	1	26
39	39	1	27
40	40	1	28
41	41	1	29
42	42	1	30
43	43	1	31
44	44	1	32
45	45	1	33
46	46	1	34
47	47	1	35
48	48	1	36
49	49	1	37
50	50	1	38
51	51	1	39
52	52	1	40
53	53	1	41
54	54	1	42
55	55	1	43
56	56	1	44
57	57	1	45
58	58	1	46
59	59	1	47
60	60	1	48
61	61	1	49
62	62	1	50
63	63	1	51
64	64	1	52
65	65	1	53
66	66	1	54
67	67	1	55
68	68	1	56
69	69	1	57
70	70	1	58
74	74	1	62
75	75	1	63
88	88	1	64
89	89	1	65
90	90	1	66
91	91	1	67
92	92	1	68
93	93	1	69
94	94	1	70
95	95	1	71
96	96	1	72
97	97	1	73
98	98	1	74
99	99	1	75
100	100	1	76
\.


--
-- Data for Name: admin_roles; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.admin_roles (id, document_id, name, code, description, created_at, updated_at, published_at, created_by_id, updated_by_id, locale) FROM stdin;
1	nml254dddaez4yq5dql7gfzt	Super Admin	strapi-super-admin	Super Admins can access and manage all features and settings.	2025-07-20 15:48:25.941	2025-07-20 15:48:25.941	2025-07-20 15:48:25.941	\N	\N	\N
2	v276ccdj22sfe770j83kxwjg	Editor	strapi-editor	Editors can manage and publish contents including those of other users.	2025-07-20 15:48:25.952	2025-07-20 15:48:25.952	2025-07-20 15:48:25.952	\N	\N	\N
3	dl6yot772qshdfym0miyqb1u	Author	strapi-author	Authors can manage the content they have created.	2025-07-20 15:48:25.958	2025-07-20 15:48:25.958	2025-07-20 15:48:25.958	\N	\N	\N
\.


--
-- Data for Name: admin_users; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.admin_users (id, document_id, firstname, lastname, username, email, password, reset_password_token, registration_token, is_active, blocked, prefered_language, created_at, updated_at, published_at, created_by_id, updated_by_id, locale) FROM stdin;
1	ndd0cisyeew7le3b9ek66csd	Ginving	Leoi	\N	<EMAIL>	$2a$10$D.PEerzoPgxBVB3tlxMgmO.4J71dLGxyXWInlhso4EO.DJ5plgkcS	\N	\N	t	f	\N	2025-07-26 22:54:31.581	2025-07-26 22:54:31.581	2025-07-26 22:54:31.581	\N	\N	\N
\.


--
-- Data for Name: admin_users_roles_lnk; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.admin_users_roles_lnk (id, user_id, role_id, role_ord, user_ord) FROM stdin;
1	1	1	1	1
\.


--
-- Data for Name: doi_collections; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.doi_collections (id, document_id, doi, created_at, updated_at, published_at, created_by_id, updated_by_id, locale) FROM stdin;
43	kk42kqwkdvr5au0ux2f1gizl	10.1111/tgis.12760	2025-07-29 01:05:01.986	2025-07-29 01:05:01.986	\N	1	1	\N
44	kk42kqwkdvr5au0ux2f1gizl	10.1111/tgis.12760	2025-07-29 01:05:01.986	2025-07-29 01:05:01.986	2025-07-29 01:05:02.012	1	1	\N
45	ficef244yrjinu9kzh402afd	10.1016/j.cities.2020.103077	2025-07-29 22:26:44.442	2025-07-29 22:26:48.312	\N	1	1	\N
46	ficef244yrjinu9kzh402afd	10.1016/j.cities.2020.103077	2025-07-29 22:26:44.442	2025-07-29 22:26:48.312	2025-07-29 22:26:48.331	1	1	\N
47	lry7llyz4j9tdvcsi5m1fob0	10.1186/1476-072X-13-42	2025-07-29 22:27:29.9	2025-07-29 22:27:32.17	\N	1	1	\N
48	lry7llyz4j9tdvcsi5m1fob0	10.1186/1476-072X-13-42	2025-07-29 22:27:29.9	2025-07-29 22:27:32.17	2025-07-29 22:27:32.189	1	1	\N
49	tmtzxerdpzkpdpyet9d8x05j	10.1016/j.compenvurbsys.2018.02.006	2025-07-29 22:27:53.51	2025-07-29 22:27:55.508	\N	1	1	\N
50	tmtzxerdpzkpdpyet9d8x05j	10.1016/j.compenvurbsys.2018.02.006	2025-07-29 22:27:53.51	2025-07-29 22:27:55.508	2025-07-29 22:27:55.524	1	1	\N
51	vx8bqi4cexdwholzoc34wzcj	10.1016/j.compenvurbsys.2022.101872	2025-07-29 22:28:16.972	2025-07-29 22:28:20.335	\N	1	1	\N
52	vx8bqi4cexdwholzoc34wzcj	10.1016/j.compenvurbsys.2022.101872	2025-07-29 22:28:16.972	2025-07-29 22:28:20.335	2025-07-29 22:28:20.344	1	1	\N
53	qtd6te7ftlxa7ur4bgm3sldv	10.1016/j.trd.2022.103514	2025-07-29 22:28:35.45	2025-07-29 22:28:37.223	\N	1	1	\N
54	qtd6te7ftlxa7ur4bgm3sldv	10.1016/j.trd.2022.103514	2025-07-29 22:28:35.45	2025-07-29 22:28:37.223	2025-07-29 22:28:37.236	1	1	\N
55	blw3gclyx68hro3qlpquv8b6	10.1371/journal.pone.0223650	2025-07-29 22:30:10.233	2025-07-29 22:30:12.331	\N	1	1	\N
56	blw3gclyx68hro3qlpquv8b6	10.1371/journal.pone.0223650	2025-07-29 22:30:10.233	2025-07-29 22:30:12.331	2025-07-29 22:30:12.342	1	1	\N
57	b45j0o6jgwu4wkzo2te88ug4	10.13203/j.whugis20200535	2025-07-29 22:30:35.668	2025-07-29 22:30:35.668	\N	1	1	\N
58	b45j0o6jgwu4wkzo2te88ug4	10.13203/j.whugis20200535	2025-07-29 22:30:35.668	2025-07-29 22:30:35.668	2025-07-29 22:30:35.681	1	1	\N
59	yn78miof8k84qeqfjiwbpwp0	10.1007/s44212-022-00018-w	2025-07-29 22:34:12.779	2025-07-29 22:34:12.779	\N	1	1	\N
60	yn78miof8k84qeqfjiwbpwp0	10.1007/s44212-022-00018-w	2025-07-29 22:34:12.779	2025-07-29 22:34:12.779	2025-07-29 22:34:12.803	1	1	\N
61	t40zynp698jokgej6jj6ecug	10.1016/j.cities.2022.104036	2025-07-29 22:34:44.412	2025-07-29 22:34:44.412	\N	1	1	\N
62	t40zynp698jokgej6jj6ecug	10.1016/j.cities.2022.104036	2025-07-29 22:34:44.412	2025-07-29 22:34:44.412	2025-07-29 22:34:44.433	1	1	\N
63	dhf8ndx2kyodmwzgyr8gzya1	10.1007/s44212-022-00003-3	2025-07-29 22:35:05.22	2025-07-29 22:35:05.22	\N	1	1	\N
64	dhf8ndx2kyodmwzgyr8gzya1	10.1007/s44212-022-00003-3	2025-07-29 22:35:05.22	2025-07-29 22:35:05.22	2025-07-29 22:35:05.234	1	1	\N
65	za9y86ra837pew345r6btldv	10.1016/j.apgeog.2022.102748	2025-07-29 22:35:24.822	2025-07-29 22:35:24.822	\N	1	1	\N
66	za9y86ra837pew345r6btldv	10.1016/j.apgeog.2022.102748	2025-07-29 22:35:24.822	2025-07-29 22:35:24.822	2025-07-29 22:35:24.842	1	1	\N
67	spage8f0jg51pcm3sa75buhx	10.1080/10095020.2022.2157761	2025-07-29 22:37:11.531	2025-07-29 22:37:11.531	\N	1	1	\N
68	spage8f0jg51pcm3sa75buhx	10.1080/10095020.2022.2157761	2025-07-29 22:37:11.531	2025-07-29 22:37:11.531	2025-07-29 22:37:11.567	1	1	\N
69	gzeb9oy74fulqsg65w6uqobp	10.1016/j.apgeog.2023.103179	2025-07-29 22:37:34.686	2025-07-29 22:37:34.686	\N	1	1	\N
70	gzeb9oy74fulqsg65w6uqobp	10.1016/j.apgeog.2023.103179	2025-07-29 22:37:34.686	2025-07-29 22:37:34.686	2025-07-29 22:37:34.706	1	1	\N
71	n8or4snulxptnfwyxwkafcxm	10.4337/9781788971089.00020	2025-07-29 22:40:16.08	2025-07-29 22:40:16.08	\N	1	1	\N
72	n8or4snulxptnfwyxwkafcxm	10.4337/9781788971089.00020	2025-07-29 22:40:16.08	2025-07-29 22:40:16.08	2025-07-29 22:40:16.099	1	1	\N
73	kmjeadiufknn688q8dejvzlj	10.5194/isprs-annals-V-4-2022-259-2022	2025-07-29 22:40:57.347	2025-07-29 22:40:57.347	\N	1	1	\N
74	kmjeadiufknn688q8dejvzlj	10.5194/isprs-annals-V-4-2022-259-2022	2025-07-29 22:40:57.347	2025-07-29 22:40:57.347	2025-07-29 22:40:57.365	1	1	\N
75	n6phjj8qxwbvo127ks5rpe4s	10.1109/JSTARS.2023.3301792	2025-07-29 22:41:30.17	2025-07-29 22:41:30.17	\N	1	1	\N
76	n6phjj8qxwbvo127ks5rpe4s	10.1109/JSTARS.2023.3301792	2025-07-29 22:41:30.17	2025-07-29 22:41:30.17	2025-07-29 22:41:30.186	1	1	\N
77	v4og026xg12lqisj0qh2dud1	10.48550/arXiv.2208.04727	2025-07-29 22:42:05.092	2025-07-29 22:42:05.092	\N	1	1	\N
78	v4og026xg12lqisj0qh2dud1	10.48550/arXiv.2208.04727	2025-07-29 22:42:05.092	2025-07-29 22:42:05.092	2025-07-29 22:42:05.111	1	1	\N
81	s633y1jcwtsu08lbw9eazf32	10.11947/j.AGCS.2021.20200310	2025-07-29 22:42:54.497	2025-07-29 22:42:54.497	\N	1	1	\N
82	s633y1jcwtsu08lbw9eazf32	10.11947/j.AGCS.2021.20200310	2025-07-29 22:42:54.497	2025-07-29 22:42:54.497	2025-07-29 22:42:54.518	1	1	\N
83	wavw0ruorlz1vlkw9ktio086	10.1177/23998083241234137	2025-07-29 22:43:14.488	2025-07-29 22:43:14.488	\N	1	1	\N
84	wavw0ruorlz1vlkw9ktio086	10.1177/23998083241234137	2025-07-29 22:43:14.488	2025-07-29 22:43:14.488	2025-07-29 22:43:14.499	1	1	\N
85	rm0vmtmulwtqmab7wtof51i0	10.1016/j.tbs.2025.101101	2025-07-29 22:44:31.556	2025-07-29 22:44:31.556	\N	1	1	\N
86	rm0vmtmulwtqmab7wtof51i0	10.1016/j.tbs.2025.101101	2025-07-29 22:44:31.556	2025-07-29 22:44:31.556	2025-07-29 22:44:31.575	1	1	\N
87	du11zof8vcel6r14vsb4nfnq	10.1016/j.apgeog.2025.103646	2025-07-29 22:44:52.13	2025-07-29 22:44:52.13	\N	1	1	\N
88	du11zof8vcel6r14vsb4nfnq	10.1016/j.apgeog.2025.103646	2025-07-29 22:44:52.13	2025-07-29 22:44:52.13	2025-07-29 22:44:52.151	1	1	\N
89	nsewloaugvz82ocg0zuy316e	10.13813/j.cn11-5141/u.2022.0410	2025-07-29 22:45:43.104	2025-07-29 22:45:43.104	\N	1	1	\N
90	nsewloaugvz82ocg0zuy316e	10.13813/j.cn11-5141/u.2022.0410	2025-07-29 22:45:43.104	2025-07-29 22:45:43.104	2025-07-29 22:45:43.114	1	1	\N
93	pk40t0olide0ni1rw1jb5z98	10.1007/s43762-025-00190-0	2025-07-29 22:55:10.547	2025-07-29 22:55:10.547	\N	1	1	\N
94	pk40t0olide0ni1rw1jb5z98	10.1007/s43762-025-00190-0	2025-07-29 22:55:10.547	2025-07-29 22:55:10.547	2025-07-29 22:55:10.571	1	1	\N
\.


--
-- Data for Name: files; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.files (id, document_id, name, alternative_text, caption, width, height, formats, hash, ext, mime, size, url, preview_url, provider, provider_metadata, folder_path, created_at, updated_at, published_at, created_by_id, updated_by_id, locale) FROM stdin;
1	e49uvjsgbfwrwzxwlsk7e18x	lqq.jpg	\N	\N	1084	1281	{"large": {"ext": ".jpg", "url": "/uploads/large_lqq_84c34cf030.jpg", "hash": "large_lqq_84c34cf030", "mime": "image/jpeg", "name": "large_lqq.jpg", "path": null, "size": 56.89, "width": 846, "height": 1000, "sizeInBytes": 56891}, "small": {"ext": ".jpg", "url": "/uploads/small_lqq_84c34cf030.jpg", "hash": "small_lqq_84c34cf030", "mime": "image/jpeg", "name": "small_lqq.jpg", "path": null, "size": 19.73, "width": 423, "height": 500, "sizeInBytes": 19726}, "medium": {"ext": ".jpg", "url": "/uploads/medium_lqq_84c34cf030.jpg", "hash": "medium_lqq_84c34cf030", "mime": "image/jpeg", "name": "medium_lqq.jpg", "path": null, "size": 37.04, "width": 635, "height": 750, "sizeInBytes": 37041}, "thumbnail": {"ext": ".jpg", "url": "/uploads/thumbnail_lqq_84c34cf030.jpg", "hash": "thumbnail_lqq_84c34cf030", "mime": "image/jpeg", "name": "thumbnail_lqq.jpg", "path": null, "size": 3.63, "width": 132, "height": 156, "sizeInBytes": 3627}}	lqq_84c34cf030	.jpg	image/jpeg	76.76	/uploads/lqq_84c34cf030.jpg	\N	local	\N	/1	2025-07-26 23:17:34.638	2025-07-26 23:18:27.569	2025-07-26 23:17:34.638	1	1	\N
2	u8ydgjul02whqio2n534kn0u	yueyang.jpg	\N	\N	1320	1667	{"large": {"ext": ".jpg", "url": "/uploads/large_yueyang_97104f07cb.jpg", "hash": "large_yueyang_97104f07cb", "mime": "image/jpeg", "name": "large_yueyang.jpg", "path": null, "size": 1299.62, "width": 792, "height": 1000, "sizeInBytes": 1299620}, "small": {"ext": ".jpg", "url": "/uploads/small_yueyang_97104f07cb.jpg", "hash": "small_yueyang_97104f07cb", "mime": "image/jpeg", "name": "small_yueyang.jpg", "path": null, "size": 346.86, "width": 396, "height": 500, "sizeInBytes": 346857}, "medium": {"ext": ".jpg", "url": "/uploads/medium_yueyang_97104f07cb.jpg", "hash": "medium_yueyang_97104f07cb", "mime": "image/jpeg", "name": "medium_yueyang.jpg", "path": null, "size": 755.42, "width": 594, "height": 750, "sizeInBytes": 755420}, "thumbnail": {"ext": ".jpg", "url": "/uploads/thumbnail_yueyang_97104f07cb.jpg", "hash": "thumbnail_yueyang_97104f07cb", "mime": "image/jpeg", "name": "thumbnail_yueyang.jpg", "path": null, "size": 39.41, "width": 124, "height": 156, "sizeInBytes": 39407}}	yueyang_97104f07cb	.jpg	image/jpeg	766.20	/uploads/yueyang_97104f07cb.jpg	\N	local	\N	/1	2025-07-27 00:43:55.687	2025-07-27 00:43:55.687	2025-07-27 00:43:55.687	1	1	\N
3	ctgj2z8j5u3rp4tv1vfganvv	gql.jpg	\N	\N	270	270	{"thumbnail": {"ext": ".jpg", "url": "/uploads/thumbnail_gql_353990813e.jpg", "hash": "thumbnail_gql_353990813e", "mime": "image/jpeg", "name": "thumbnail_gql.jpg", "path": null, "size": 7.54, "width": 156, "height": 156, "sizeInBytes": 7541}}	gql_353990813e	.jpg	image/jpeg	16.72	/uploads/gql_353990813e.jpg	\N	local	\N	/1	2025-07-27 00:49:03.254	2025-07-27 00:49:03.254	2025-07-27 00:49:03.254	1	1	\N
4	zabv0cngrsw9nd84lo2ppdxn	zhongchen.jpeg	\N	\N	180	180	{"thumbnail": {"ext": ".jpeg", "url": "/uploads/thumbnail_zhongchen_8fbe509281.jpeg", "hash": "thumbnail_zhongchen_8fbe509281", "mime": "image/jpeg", "name": "thumbnail_zhongchen.jpeg", "path": null, "size": 6.36, "width": 156, "height": 156, "sizeInBytes": 6362}}	zhongchen_8fbe509281	.jpeg	image/jpeg	7.70	/uploads/zhongchen_8fbe509281.jpeg	\N	local	\N	/1	2025-07-28 20:40:57.081	2025-07-28 20:40:57.081	2025-07-28 20:40:57.082	1	1	\N
5	d3khpvpsmp2nphaixnjla2ov	cjz.jpg	\N	\N	270	270	{"thumbnail": {"ext": ".jpg", "url": "/uploads/thumbnail_cjz_76e7373200.jpg", "hash": "thumbnail_cjz_76e7373200", "mime": "image/jpeg", "name": "thumbnail_cjz.jpg", "path": null, "size": 3.91, "width": 156, "height": 156, "sizeInBytes": 3912}}	cjz_76e7373200	.jpg	image/jpeg	7.89	/uploads/cjz_76e7373200.jpg	\N	local	\N	/1	2025-07-28 20:50:47.974	2025-07-28 20:50:47.974	2025-07-28 20:50:47.975	1	1	\N
6	o5fr3yi9txklsdfjbdepoaf8	cr.png	\N	\N	224	224	{"thumbnail": {"ext": ".png", "url": "/uploads/thumbnail_cr_49c991420d.png", "hash": "thumbnail_cr_49c991420d", "mime": "image/png", "name": "thumbnail_cr.png", "path": null, "size": 66.25, "width": 156, "height": 156, "sizeInBytes": 66251}}	cr_49c991420d	.png	image/png	39.74	/uploads/cr_49c991420d.png	\N	local	\N	/1	2025-07-28 20:57:04.472	2025-07-28 20:57:04.472	2025-07-28 20:57:04.472	1	1	\N
\.


--
-- Data for Name: files_folder_lnk; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.files_folder_lnk (id, file_id, folder_id, file_ord) FROM stdin;
1	1	1	1
2	2	1	2
3	3	1	3
4	4	1	4
5	5	1	5
6	6	1	6
\.


--
-- Data for Name: files_related_mph; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.files_related_mph (id, file_id, related_id, related_type, field, "order") FROM stdin;
31	2	3	api::team-member.team-member	avatar	1
32	2	25	api::team-member.team-member	avatar	1
34	1	1	api::team-member.team-member	avatar	1
35	1	26	api::team-member.team-member	avatar	1
37	3	6	api::team-member.team-member	avatar	1
38	3	27	api::team-member.team-member	avatar	1
45	4	31	api::team-member.team-member	avatar	1
46	4	37	api::team-member.team-member	avatar	1
47	5	33	api::team-member.team-member	avatar	1
48	5	38	api::team-member.team-member	avatar	1
49	6	35	api::team-member.team-member	avatar	1
50	6	39	api::team-member.team-member	avatar	1
\.


--
-- Data for Name: i18n_locale; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.i18n_locale (id, document_id, name, code, created_at, updated_at, published_at, created_by_id, updated_by_id, locale) FROM stdin;
1	wn9k0bo4sl43uadyib6y2cic	English (en)	en	2025-07-20 15:48:25.804	2025-07-20 15:48:25.804	2025-07-20 15:48:25.804	\N	\N	\N
\.


--
-- Data for Name: paper_infos; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.paper_infos (id, document_id, doi, title, authors, year, journal, abstract, bibtex, created_at, updated_at, published_at, created_by_id, updated_by_id, locale) FROM stdin;
5	x4r79zy1f3gktf1sk0wrxl1l	10.1111/tgis.12760	Segregation or integration? Exploring activity disparities between migrants and settled urban residents using human mobility data	Qi‐Li Gao, Yang Yue, Wei Tu, Jinzhou Cao, Qing‐Quan Li	2021	Transactions in GIS	<jats:title>Abstract</jats:title><jats:p>Due to the difficulty of tracking large numbers of new migrants, how their daily activity behaviors differ from those of settled residents has not been well investigated, leading to a lack of understanding of new migrants' integration. Meanwhile, existing research largely emphasized residential space and ignored other activity disparities. To obtain a more comprehensive picture of urban segregation, we identified new migrants and two settled urban groups from two kinds of human mobility data. A S‐T‐A‐D‐I interactive framework was proposed to measure segregation from multiple activity dimensions, including spatial colocation, temporal coexistence, accessibility, activity diversity, and social interaction. Two‐scale analysis of spatial colocation patterns reveals residential segregation by both residential location and housing type, suggesting the effectiveness of the mobility data in profiling socioeconomic groups. The temporal disparity in undertaking activities was unveiled by identifying temporal coexistence patterns. Moreover, the groups presented significant inequality in accessibility owing to the use of different travel modes, leading to a notable disparity in activity diversity. Jointly determined by the disparities in space, time, and diversity, the three groups generated a high level of self‐segregation, and new migrants and transit users presented very low interaction potentials with the car group.</jats:p>	 @article{Gao_2021, title={Segregation or integration? Exploring activity disparities between migrants and settled urban residents using human mobility data}, volume={25}, ISSN={1467-9671}, url={http://dx.doi.org/10.1111/tgis.12760}, DOI={10.1111/tgis.12760}, number={6}, journal={Transactions in GIS}, publisher={Wiley}, author={Gao, Qi‐Li and Yue, Yang and Tu, Wei and Cao, Jinzhou and Li, Qing‐Quan}, year={2021}, month=may, pages={2791–2820} }\n	2025-07-29 01:05:05.02	2025-07-29 01:05:05.02	\N	\N	\N	\N
6	x4r79zy1f3gktf1sk0wrxl1l	10.1111/tgis.12760	Segregation or integration? Exploring activity disparities between migrants and settled urban residents using human mobility data	Qi‐Li Gao, Yang Yue, Wei Tu, Jinzhou Cao, Qing‐Quan Li	2021	Transactions in GIS	<jats:title>Abstract</jats:title><jats:p>Due to the difficulty of tracking large numbers of new migrants, how their daily activity behaviors differ from those of settled residents has not been well investigated, leading to a lack of understanding of new migrants' integration. Meanwhile, existing research largely emphasized residential space and ignored other activity disparities. To obtain a more comprehensive picture of urban segregation, we identified new migrants and two settled urban groups from two kinds of human mobility data. A S‐T‐A‐D‐I interactive framework was proposed to measure segregation from multiple activity dimensions, including spatial colocation, temporal coexistence, accessibility, activity diversity, and social interaction. Two‐scale analysis of spatial colocation patterns reveals residential segregation by both residential location and housing type, suggesting the effectiveness of the mobility data in profiling socioeconomic groups. The temporal disparity in undertaking activities was unveiled by identifying temporal coexistence patterns. Moreover, the groups presented significant inequality in accessibility owing to the use of different travel modes, leading to a notable disparity in activity diversity. Jointly determined by the disparities in space, time, and diversity, the three groups generated a high level of self‐segregation, and new migrants and transit users presented very low interaction potentials with the car group.</jats:p>	 @article{Gao_2021, title={Segregation or integration? Exploring activity disparities between migrants and settled urban residents using human mobility data}, volume={25}, ISSN={1467-9671}, url={http://dx.doi.org/10.1111/tgis.12760}, DOI={10.1111/tgis.12760}, number={6}, journal={Transactions in GIS}, publisher={Wiley}, author={Gao, Qi‐Li and Yue, Yang and Tu, Wei and Cao, Jinzhou and Li, Qing‐Quan}, year={2021}, month=may, pages={2791–2820} }\n	2025-07-29 01:05:05.02	2025-07-29 01:05:05.02	2025-07-29 01:05:05.033	\N	\N	\N
7	y19shdldjhmdt5phcqw77un0	10.1016/j.cities.2020.103077	Resolving urban mobility networks from individual travel graphs using massive-scale mobile phone tracking data	Jinzhou Cao, Qingquan Li, Wei Tu, Qili Gao, Rui Cao, Chen Zhong	2021	Cities	\N	 @article{Cao_2021, title={Resolving urban mobility networks from individual travel graphs using massive-scale mobile phone tracking data}, volume={110}, ISSN={0264-2751}, url={http://dx.doi.org/10.1016/j.cities.2020.103077}, DOI={10.1016/j.cities.2020.103077}, journal={Cities}, publisher={Elsevier BV}, author={Cao, Jinzhou and Li, Qingquan and Tu, Wei and Gao, Qili and Cao, Rui and Zhong, Chen}, year={2021}, month=mar, pages={103077} }\n	2025-07-29 22:26:47.034	2025-07-29 22:26:47.034	\N	\N	\N	\N
8	y19shdldjhmdt5phcqw77un0	10.1016/j.cities.2020.103077	Resolving urban mobility networks from individual travel graphs using massive-scale mobile phone tracking data	Jinzhou Cao, Qingquan Li, Wei Tu, Qili Gao, Rui Cao, Chen Zhong	2021	Cities	\N	 @article{Cao_2021, title={Resolving urban mobility networks from individual travel graphs using massive-scale mobile phone tracking data}, volume={110}, ISSN={0264-2751}, url={http://dx.doi.org/10.1016/j.cities.2020.103077}, DOI={10.1016/j.cities.2020.103077}, journal={Cities}, publisher={Elsevier BV}, author={Cao, Jinzhou and Li, Qingquan and Tu, Wei and Gao, Qili and Cao, Rui and Zhong, Chen}, year={2021}, month=mar, pages={103077} }\n	2025-07-29 22:26:47.034	2025-07-29 22:26:47.034	2025-07-29 22:26:47.056	\N	\N	\N
9	g8lampz3vm0lfx8gjbwzcqn4	10.1186/1476-072X-13-42	Selecting the optimal healthcare centers with a modified P-median model: a visual analytic perspective	Tao Jia, Hongbing Tao, Kun Qin, Yulong Wang, Chengkun Liu, Qili Gao	2014	International Journal of Health Geographics	\N	 @article{Jia_2014, title={Selecting the optimal healthcare centers with a modified P-median model: a visual analytic perspective}, volume={13}, ISSN={1476-072X}, url={http://dx.doi.org/10.1186/1476-072X-13-42}, DOI={10.1186/1476-072x-13-42}, number={1}, journal={International Journal of Health Geographics}, publisher={Springer Science and Business Media LLC}, author={Jia, Tao and Tao, Hongbing and Qin, Kun and Wang, Yulong and Liu, Chengkun and Gao, Qili}, year={2014}, month=oct }\n	2025-07-29 22:27:34.048	2025-07-29 22:27:34.048	\N	\N	\N	\N
10	g8lampz3vm0lfx8gjbwzcqn4	10.1186/1476-072X-13-42	Selecting the optimal healthcare centers with a modified P-median model: a visual analytic perspective	Tao Jia, Hongbing Tao, Kun Qin, Yulong Wang, Chengkun Liu, Qili Gao	2014	International Journal of Health Geographics	\N	 @article{Jia_2014, title={Selecting the optimal healthcare centers with a modified P-median model: a visual analytic perspective}, volume={13}, ISSN={1476-072X}, url={http://dx.doi.org/10.1186/1476-072X-13-42}, DOI={10.1186/1476-072x-13-42}, number={1}, journal={International Journal of Health Geographics}, publisher={Springer Science and Business Media LLC}, author={Jia, Tao and Tao, Hongbing and Qin, Kun and Wang, Yulong and Liu, Chengkun and Gao, Qili}, year={2014}, month=oct }\n	2025-07-29 22:27:34.048	2025-07-29 22:27:34.048	2025-07-29 22:27:34.06	\N	\N	\N
11	acieussy09ignecrscnnps5g	10.1016/j.compenvurbsys.2018.02.006	Exploring changes in the spatial distribution of the low-to-moderate income group using transit smart card data	Qi-Li Gao, Qing-Quan Li, Yang Yue, Yan Zhuang, Zhi-Peng Chen, Hui Kong	2018	Computers, Environment and Urban Systems	\N	 @article{Gao_2018, title={Exploring changes in the spatial distribution of the low-to-moderate income group using transit smart card data}, volume={72}, ISSN={0198-9715}, url={http://dx.doi.org/10.1016/j.compenvurbsys.2018.02.006}, DOI={10.1016/j.compenvurbsys.2018.02.006}, journal={Computers, Environment and Urban Systems}, publisher={Elsevier BV}, author={Gao, Qi-Li and Li, Qing-Quan and Yue, Yang and Zhuang, Yan and Chen, Zhi-Peng and Kong, Hui}, year={2018}, month=nov, pages={68–77} }\n	2025-07-29 22:27:56.076	2025-07-29 22:27:56.076	\N	\N	\N	\N
12	acieussy09ignecrscnnps5g	10.1016/j.compenvurbsys.2018.02.006	Exploring changes in the spatial distribution of the low-to-moderate income group using transit smart card data	Qi-Li Gao, Qing-Quan Li, Yang Yue, Yan Zhuang, Zhi-Peng Chen, Hui Kong	2018	Computers, Environment and Urban Systems	\N	 @article{Gao_2018, title={Exploring changes in the spatial distribution of the low-to-moderate income group using transit smart card data}, volume={72}, ISSN={0198-9715}, url={http://dx.doi.org/10.1016/j.compenvurbsys.2018.02.006}, DOI={10.1016/j.compenvurbsys.2018.02.006}, journal={Computers, Environment and Urban Systems}, publisher={Elsevier BV}, author={Gao, Qi-Li and Li, Qing-Quan and Yue, Yang and Zhuang, Yan and Chen, Zhi-Peng and Kong, Hui}, year={2018}, month=nov, pages={68–77} }\n	2025-07-29 22:27:56.076	2025-07-29 22:27:56.076	2025-07-29 22:27:56.098	\N	\N	\N
13	y3ilg1dr9c9ylxzxbml749a2	10.1016/j.compenvurbsys.2022.101872	Delineating urban functional zones using mobile phone data: A case study of cross-boundary integration in Shenzhen-Dongguan-Huizhou area	Bowen Zhang, Chen Zhong, Qili Gao, Zahratu Shabrina, Wei Tu	2022	Computers, Environment and Urban Systems	\N	 @article{Zhang_2022, title={Delineating urban functional zones using mobile phone data: A case study of cross-boundary integration in Shenzhen-Dongguan-Huizhou area}, volume={98}, ISSN={0198-9715}, url={http://dx.doi.org/10.1016/j.compenvurbsys.2022.101872}, DOI={10.1016/j.compenvurbsys.2022.101872}, journal={Computers, Environment and Urban Systems}, publisher={Elsevier BV}, author={Zhang, Bowen and Zhong, Chen and Gao, Qili and Shabrina, Zahratu and Tu, Wei}, year={2022}, month=dec, pages={101872} }\n	2025-07-29 22:28:18.85	2025-07-29 22:28:18.85	\N	\N	\N	\N
15	tdpztgq5ic5jeyjw4ul985mn	10.1016/j.trd.2022.103514	An extended node-place model for comparative studies of transit-oriented development	Yongxin Yang, Chen Zhong, Qi-Li Gao	2022	Transportation Research Part D: Transport and Environment	\N	 @article{Yang_2022, title={An extended node-place model for comparative studies of transit-oriented development}, volume={113}, ISSN={1361-9209}, url={http://dx.doi.org/10.1016/j.trd.2022.103514}, DOI={10.1016/j.trd.2022.103514}, journal={Transportation Research Part D: Transport and Environment}, publisher={Elsevier BV}, author={Yang, Yongxin and Zhong, Chen and Gao, Qi-Li}, year={2022}, month=dec, pages={103514} }\n	2025-07-29 22:28:38.609	2025-07-29 22:28:38.609	\N	\N	\N	\N
18	r2h5jcsdcnnjnud4edka04zn	10.1371/journal.pone.0223650	Urban commuting dynamics in response to public transit upgrades: A big data approach	Qi-Li Gao, Qing-Quan Li, Yan Zhuang, Yang Yue, Zhen-Zhen Liu, Shui-Quan Li, Daniel Sui	2019	PLOS ONE	\N	 @article{Gao_2019, title={Urban commuting dynamics in response to public transit upgrades: A big data approach}, volume={14}, ISSN={1932-6203}, url={http://dx.doi.org/10.1371/journal.pone.0223650}, DOI={10.1371/journal.pone.0223650}, number={10}, journal={PLOS ONE}, publisher={Public Library of Science (PLoS)}, author={Gao, Qi-Li and Li, Qing-Quan and Zhuang, Yan and Yue, Yang and Liu, Zhen-Zhen and Li, Shui-Quan and Sui, Daniel}, editor={Kahn, Matthew}, year={2019}, month=oct, pages={e0223650} }\n	2025-07-29 22:30:12.91	2025-07-29 22:30:12.91	2025-07-29 22:30:12.923	\N	\N	\N
22	elekz38ycdpi7a4l5w1cryfb	10.1007/s44212-022-00018-w	Understanding internal migration in the UK before and during the COVID-19 pandemic using twitter data	Yikang Wang, Chen Zhong, Qili Gao, Carmen Cabrera-Arnau	2022	Urban Informatics	<jats:title>Abstract</jats:title><jats:p>The COVID-19 pandemic has greatly affected internal migration patterns and may last beyond the pandemic. It raises the need to monitor the migration in an economical, effective and timely way. Benefitting from the advancement of geolocation data collection techniques, we used near real-time and fine-grained Twitter data to monitor migration patterns during the COVID-19 pandemic, dated from January 2019 to December 2021. Based on geocoding and estimating home locations, we proposed five indices depicting migration patterns, which are demonstrated by applying an empirical study at national and local authority scales to the UK. Our findings point to complex social processes unfolding differently over space and time. In particular, the pandemic and lockdown policies significantly reduced the rate of migration. Furthermore, we found a trend of people moving out of large cities to the nearby rural areas, and also conjunctive cities if there is one, before and during the peak of the pandemic. The trend of moving to rural areas became more significant in 2020 and most people who moved out had not returned by the end of 2021, although large cities recovered more quickly than other regions. Our results of monthly migration matrixes are validated to be consistent with official migration flow data released by the Office for National Statistics, but have finer temporal granularity and can be updated more frequently. This study demonstrates that Twitter data is highly valuable for migration trend analysis despite the biases in population representation.</jats:p>	 @article{Wang_2022, title={Understanding internal migration in the UK before and during the COVID-19 pandemic using twitter data}, volume={1}, ISSN={2731-6963}, url={http://dx.doi.org/10.1007/s44212-022-00018-w}, DOI={10.1007/s44212-022-00018-w}, number={1}, journal={Urban Informatics}, publisher={Springer Science and Business Media LLC}, author={Wang, Yikang and Zhong, Chen and Gao, Qili and Cabrera-Arnau, Carmen}, year={2022}, month=nov }\n	2025-07-29 22:34:15.106	2025-07-29 22:34:15.106	2025-07-29 22:34:15.153	\N	\N	\N
23	eyvfptvrj3239w6lctm22cks	10.1016/j.cities.2022.104036	Revealing transport inequality from an activity space perspective: A study based on human mobility data	Qi-Li Gao, Yang Yue, Chen Zhong, Jinzhou Cao, Wei Tu, Qing-Quan Li	2022	Cities	\N	 @article{Gao_2022, title={Revealing transport inequality from an activity space perspective: A study based on human mobility data}, volume={131}, ISSN={0264-2751}, url={http://dx.doi.org/10.1016/j.cities.2022.104036}, DOI={10.1016/j.cities.2022.104036}, journal={Cities}, publisher={Elsevier BV}, author={Gao, Qi-Li and Yue, Yang and Zhong, Chen and Cao, Jinzhou and Tu, Wei and Li, Qing-Quan}, year={2022}, month=dec, pages={104036} }\n	2025-07-29 22:34:47.264	2025-07-29 22:34:47.264	\N	\N	\N	\N
27	qcsxa4efz1yuvj98082riyba	10.1016/j.apgeog.2022.102748	Identifying localized amenities for gentrification using a machine learning-based framework	Jin Zeng, Yang Yue, Qili Gao, Yanyan Gu, Chenglin Ma	2022	Applied Geography	\N	 @article{Zeng_2022, title={Identifying localized amenities for gentrification using a machine learning-based framework}, volume={145}, ISSN={0143-6228}, url={http://dx.doi.org/10.1016/j.apgeog.2022.102748}, DOI={10.1016/j.apgeog.2022.102748}, journal={Applied Geography}, publisher={Elsevier BV}, author={Zeng, Jin and Yue, Yang and Gao, Qili and Gu, Yanyan and Ma, Chenglin}, year={2022}, month=aug, pages={102748} }\n	2025-07-29 22:35:27.205	2025-07-29 22:35:27.205	\N	\N	\N	\N
30	m3lb7vcv7aornj0jxyle4xzf	10.1080/10095020.2022.2157761	Untangling the association between urban mobility and urban elements	Jinzhou Cao, Wei Tu, Rui Cao, Qili Gao, Guanzhou Chen, Qingquan Li	2023	Geo-spatial Information Science	\N	 @article{Cao_2023, title={Untangling the association between urban mobility and urban elements}, volume={27}, ISSN={1993-5153}, url={http://dx.doi.org/10.1080/10095020.2022.2157761}, DOI={10.1080/10095020.2022.2157761}, number={4}, journal={Geo-spatial Information Science}, publisher={Informa UK Limited}, author={Cao, Jinzhou and Tu, Wei and Cao, Rui and Gao, Qili and Chen, Guanzhou and Li, Qingquan}, year={2023}, month=feb, pages={1071–1089} }\n	2025-07-29 22:37:15.752	2025-07-29 22:37:15.752	2025-07-29 22:37:15.766	\N	\N	\N
14	y3ilg1dr9c9ylxzxbml749a2	10.1016/j.compenvurbsys.2022.101872	Delineating urban functional zones using mobile phone data: A case study of cross-boundary integration in Shenzhen-Dongguan-Huizhou area	Bowen Zhang, Chen Zhong, Qili Gao, Zahratu Shabrina, Wei Tu	2022	Computers, Environment and Urban Systems	\N	 @article{Zhang_2022, title={Delineating urban functional zones using mobile phone data: A case study of cross-boundary integration in Shenzhen-Dongguan-Huizhou area}, volume={98}, ISSN={0198-9715}, url={http://dx.doi.org/10.1016/j.compenvurbsys.2022.101872}, DOI={10.1016/j.compenvurbsys.2022.101872}, journal={Computers, Environment and Urban Systems}, publisher={Elsevier BV}, author={Zhang, Bowen and Zhong, Chen and Gao, Qili and Shabrina, Zahratu and Tu, Wei}, year={2022}, month=dec, pages={101872} }\n	2025-07-29 22:28:18.85	2025-07-29 22:28:18.85	2025-07-29 22:28:18.866	\N	\N	\N
16	tdpztgq5ic5jeyjw4ul985mn	10.1016/j.trd.2022.103514	An extended node-place model for comparative studies of transit-oriented development	Yongxin Yang, Chen Zhong, Qi-Li Gao	2022	Transportation Research Part D: Transport and Environment	\N	 @article{Yang_2022, title={An extended node-place model for comparative studies of transit-oriented development}, volume={113}, ISSN={1361-9209}, url={http://dx.doi.org/10.1016/j.trd.2022.103514}, DOI={10.1016/j.trd.2022.103514}, journal={Transportation Research Part D: Transport and Environment}, publisher={Elsevier BV}, author={Yang, Yongxin and Zhong, Chen and Gao, Qi-Li}, year={2022}, month=dec, pages={103514} }\n	2025-07-29 22:28:38.609	2025-07-29 22:28:38.609	2025-07-29 22:28:38.625	\N	\N	\N
17	r2h5jcsdcnnjnud4edka04zn	10.1371/journal.pone.0223650	Urban commuting dynamics in response to public transit upgrades: A big data approach	Qi-Li Gao, Qing-Quan Li, Yan Zhuang, Yang Yue, Zhen-Zhen Liu, Shui-Quan Li, Daniel Sui	2019	PLOS ONE	\N	 @article{Gao_2019, title={Urban commuting dynamics in response to public transit upgrades: A big data approach}, volume={14}, ISSN={1932-6203}, url={http://dx.doi.org/10.1371/journal.pone.0223650}, DOI={10.1371/journal.pone.0223650}, number={10}, journal={PLOS ONE}, publisher={Public Library of Science (PLoS)}, author={Gao, Qi-Li and Li, Qing-Quan and Zhuang, Yan and Yue, Yang and Liu, Zhen-Zhen and Li, Shui-Quan and Sui, Daniel}, editor={Kahn, Matthew}, year={2019}, month=oct, pages={e0223650} }\n	2025-07-29 22:30:12.91	2025-07-29 22:30:12.91	\N	\N	\N	\N
31	ctllmahf1ygs13g0k1jwvlqo	10.1016/j.apgeog.2023.103179	Income estimation based on human mobility patterns and machine learning models	Qi-Li Gao, Chen Zhong, Yang Yue, Rui Cao, Bowen Zhang	2024	Applied Geography	\N	 @article{Gao_2024, title={Income estimation based on human mobility patterns and machine learning models}, volume={163}, ISSN={0143-6228}, url={http://dx.doi.org/10.1016/j.apgeog.2023.103179}, DOI={10.1016/j.apgeog.2023.103179}, journal={Applied Geography}, publisher={Elsevier BV}, author={Gao, Qi-Li and Zhong, Chen and Yue, Yang and Cao, Rui and Zhang, Bowen}, year={2024}, month=feb, pages={103179} }\n	2025-07-29 22:37:37.209	2025-07-29 22:37:37.209	\N	\N	\N	\N
32	ctllmahf1ygs13g0k1jwvlqo	10.1016/j.apgeog.2023.103179	Income estimation based on human mobility patterns and machine learning models	Qi-Li Gao, Chen Zhong, Yang Yue, Rui Cao, Bowen Zhang	2024	Applied Geography	\N	 @article{Gao_2024, title={Income estimation based on human mobility patterns and machine learning models}, volume={163}, ISSN={0143-6228}, url={http://dx.doi.org/10.1016/j.apgeog.2023.103179}, DOI={10.1016/j.apgeog.2023.103179}, journal={Applied Geography}, publisher={Elsevier BV}, author={Gao, Qi-Li and Zhong, Chen and Yue, Yang and Cao, Rui and Zhang, Bowen}, year={2024}, month=feb, pages={103179} }\n	2025-07-29 22:37:37.209	2025-07-29 22:37:37.209	2025-07-29 22:37:37.223	\N	\N	\N
33	wkh1okvk9dhur1jwm7cy9nbe	10.4337/9781788971089.00020	Big data, urban analytics and the planning of smart cities	Anthony G.O. Yeh, Yang Yue, Xingang Zhou, Qi-Li Gao	2020	Handbook of Planning Support Science	\N	 @inbook{Yeh_2020, title={Big data, urban analytics and the planning of smart cities}, ISBN={9781788971072}, url={http://dx.doi.org/10.4337/9781788971089.00020}, DOI={10.4337/9781788971089.00020}, booktitle={Handbook of Planning Support Science}, publisher={Edward Elgar Publishing}, author={Yeh, Anthony G.O. and Yue, Yang and Zhou, Xingang and Gao, Qi-Li}, year={2020}, month=feb }\n	2025-07-29 22:40:18.946	2025-07-29 22:40:18.946	\N	\N	\N	\N
34	wkh1okvk9dhur1jwm7cy9nbe	10.4337/9781788971089.00020	Big data, urban analytics and the planning of smart cities	Anthony G.O. Yeh, Yang Yue, Xingang Zhou, Qi-Li Gao	2020	Handbook of Planning Support Science	\N	 @inbook{Yeh_2020, title={Big data, urban analytics and the planning of smart cities}, ISBN={9781788971072}, url={http://dx.doi.org/10.4337/9781788971089.00020}, DOI={10.4337/9781788971089.00020}, booktitle={Handbook of Planning Support Science}, publisher={Edward Elgar Publishing}, author={Yeh, Anthony G.O. and Yue, Yang and Zhou, Xingang and Gao, Qi-Li}, year={2020}, month=feb }\n	2025-07-29 22:40:18.946	2025-07-29 22:40:18.946	2025-07-29 22:40:18.958	\N	\N	\N
21	elekz38ycdpi7a4l5w1cryfb	10.1007/s44212-022-00018-w	Understanding internal migration in the UK before and during the COVID-19 pandemic using twitter data	Yikang Wang, Chen Zhong, Qili Gao, Carmen Cabrera-Arnau	2022	Urban Informatics	<jats:title>Abstract</jats:title><jats:p>The COVID-19 pandemic has greatly affected internal migration patterns and may last beyond the pandemic. It raises the need to monitor the migration in an economical, effective and timely way. Benefitting from the advancement of geolocation data collection techniques, we used near real-time and fine-grained Twitter data to monitor migration patterns during the COVID-19 pandemic, dated from January 2019 to December 2021. Based on geocoding and estimating home locations, we proposed five indices depicting migration patterns, which are demonstrated by applying an empirical study at national and local authority scales to the UK. Our findings point to complex social processes unfolding differently over space and time. In particular, the pandemic and lockdown policies significantly reduced the rate of migration. Furthermore, we found a trend of people moving out of large cities to the nearby rural areas, and also conjunctive cities if there is one, before and during the peak of the pandemic. The trend of moving to rural areas became more significant in 2020 and most people who moved out had not returned by the end of 2021, although large cities recovered more quickly than other regions. Our results of monthly migration matrixes are validated to be consistent with official migration flow data released by the Office for National Statistics, but have finer temporal granularity and can be updated more frequently. This study demonstrates that Twitter data is highly valuable for migration trend analysis despite the biases in population representation.</jats:p>	 @article{Wang_2022, title={Understanding internal migration in the UK before and during the COVID-19 pandemic using twitter data}, volume={1}, ISSN={2731-6963}, url={http://dx.doi.org/10.1007/s44212-022-00018-w}, DOI={10.1007/s44212-022-00018-w}, number={1}, journal={Urban Informatics}, publisher={Springer Science and Business Media LLC}, author={Wang, Yikang and Zhong, Chen and Gao, Qili and Cabrera-Arnau, Carmen}, year={2022}, month=nov }\n	2025-07-29 22:34:15.106	2025-07-29 22:34:15.106	\N	\N	\N	\N
24	eyvfptvrj3239w6lctm22cks	10.1016/j.cities.2022.104036	Revealing transport inequality from an activity space perspective: A study based on human mobility data	Qi-Li Gao, Yang Yue, Chen Zhong, Jinzhou Cao, Wei Tu, Qing-Quan Li	2022	Cities	\N	 @article{Gao_2022, title={Revealing transport inequality from an activity space perspective: A study based on human mobility data}, volume={131}, ISSN={0264-2751}, url={http://dx.doi.org/10.1016/j.cities.2022.104036}, DOI={10.1016/j.cities.2022.104036}, journal={Cities}, publisher={Elsevier BV}, author={Gao, Qi-Li and Yue, Yang and Zhong, Chen and Cao, Jinzhou and Tu, Wei and Li, Qing-Quan}, year={2022}, month=dec, pages={104036} }\n	2025-07-29 22:34:47.264	2025-07-29 22:34:47.264	2025-07-29 22:34:47.279	\N	\N	\N
25	j0u2889ujxy3p7v4ydaiebtq	10.1007/s44212-022-00003-3	Towards a new paradigm for segregation measurement in an age of big data	Qing-Quan Li, Yang Yue, Qi-Li Gao, Chen Zhong, Joana Barros	2022	Urban Informatics	<jats:title>Abstract</jats:title><jats:p>Recent theoretical and methodological advances in activity space and big data provide new opportunities to study socio-spatial segregation. This review first provides an overview of the literature in terms of measurements, spatial patterns, underlying causes, and social consequences of spatial segregation. These studies are mainly place-centred and static, ignoring the segregation experience across various activity spaces due to the dynamism of movements. In response to this challenge, we highlight the work in progress toward a new paradigm for segregation studies. Specifically, this review presents how and the extent to which activity space methods can advance segregation research from a people-based perspective. It explains the requirements of mobility-based methods for quantifying the dynamics of segregation due to high movement within the urban context. It then discusses and illustrates a dynamic and multi-dimensional framework to show how big data can enhance understanding segregation by capturing individuals’ spatio-temporal behaviours. The review closes with new directions and challenges for segregation research using big data.</jats:p>	 @article{Li_2022, title={Towards a new paradigm for segregation measurement in an age of big data}, volume={1}, ISSN={2731-6963}, url={http://dx.doi.org/10.1007/s44212-022-00003-3}, DOI={10.1007/s44212-022-00003-3}, number={1}, journal={Urban Informatics}, publisher={Springer Science and Business Media LLC}, author={Li, Qing-Quan and Yue, Yang and Gao, Qi-Li and Zhong, Chen and Barros, Joana}, year={2022}, month=sep }\n	2025-07-29 22:35:06.915	2025-07-29 22:35:06.915	\N	\N	\N	\N
26	j0u2889ujxy3p7v4ydaiebtq	10.1007/s44212-022-00003-3	Towards a new paradigm for segregation measurement in an age of big data	Qing-Quan Li, Yang Yue, Qi-Li Gao, Chen Zhong, Joana Barros	2022	Urban Informatics	<jats:title>Abstract</jats:title><jats:p>Recent theoretical and methodological advances in activity space and big data provide new opportunities to study socio-spatial segregation. This review first provides an overview of the literature in terms of measurements, spatial patterns, underlying causes, and social consequences of spatial segregation. These studies are mainly place-centred and static, ignoring the segregation experience across various activity spaces due to the dynamism of movements. In response to this challenge, we highlight the work in progress toward a new paradigm for segregation studies. Specifically, this review presents how and the extent to which activity space methods can advance segregation research from a people-based perspective. It explains the requirements of mobility-based methods for quantifying the dynamics of segregation due to high movement within the urban context. It then discusses and illustrates a dynamic and multi-dimensional framework to show how big data can enhance understanding segregation by capturing individuals’ spatio-temporal behaviours. The review closes with new directions and challenges for segregation research using big data.</jats:p>	 @article{Li_2022, title={Towards a new paradigm for segregation measurement in an age of big data}, volume={1}, ISSN={2731-6963}, url={http://dx.doi.org/10.1007/s44212-022-00003-3}, DOI={10.1007/s44212-022-00003-3}, number={1}, journal={Urban Informatics}, publisher={Springer Science and Business Media LLC}, author={Li, Qing-Quan and Yue, Yang and Gao, Qi-Li and Zhong, Chen and Barros, Joana}, year={2022}, month=sep }\n	2025-07-29 22:35:06.915	2025-07-29 22:35:06.915	2025-07-29 22:35:06.929	\N	\N	\N
28	qcsxa4efz1yuvj98082riyba	10.1016/j.apgeog.2022.102748	Identifying localized amenities for gentrification using a machine learning-based framework	Jin Zeng, Yang Yue, Qili Gao, Yanyan Gu, Chenglin Ma	2022	Applied Geography	\N	 @article{Zeng_2022, title={Identifying localized amenities for gentrification using a machine learning-based framework}, volume={145}, ISSN={0143-6228}, url={http://dx.doi.org/10.1016/j.apgeog.2022.102748}, DOI={10.1016/j.apgeog.2022.102748}, journal={Applied Geography}, publisher={Elsevier BV}, author={Zeng, Jin and Yue, Yang and Gao, Qili and Gu, Yanyan and Ma, Chenglin}, year={2022}, month=aug, pages={102748} }\n	2025-07-29 22:35:27.205	2025-07-29 22:35:27.205	2025-07-29 22:35:27.212	\N	\N	\N
29	m3lb7vcv7aornj0jxyle4xzf	10.1080/10095020.2022.2157761	Untangling the association between urban mobility and urban elements	Jinzhou Cao, Wei Tu, Rui Cao, Qili Gao, Guanzhou Chen, Qingquan Li	2023	Geo-spatial Information Science	\N	 @article{Cao_2023, title={Untangling the association between urban mobility and urban elements}, volume={27}, ISSN={1993-5153}, url={http://dx.doi.org/10.1080/10095020.2022.2157761}, DOI={10.1080/10095020.2022.2157761}, number={4}, journal={Geo-spatial Information Science}, publisher={Informa UK Limited}, author={Cao, Jinzhou and Tu, Wei and Cao, Rui and Gao, Qili and Chen, Guanzhou and Li, Qingquan}, year={2023}, month=feb, pages={1071–1089} }\n	2025-07-29 22:37:15.752	2025-07-29 22:37:15.752	\N	\N	\N	\N
35	ua7ebdwtqib7kk0ra7wbrq12	10.5194/isprs-annals-V-4-2022-259-2022	MACHINE LEARNING-BASED ECONOMIC DEVELOPMENT MAPPING FROM MULTI-SOURCE OPEN GEOSPATIAL DATA	R. Cao, W. Tu, J. Cai, T. Zhao, J. Xiao, J. Cao, Q. Gao, H. Su	2022	ISPRS Annals of the Photogrammetry, Remote Sensing and Spatial Information Sciences	<jats:p>Abstract. Timely and accurate socioeconomic indicators are the prerequisite for smart social governance. For example, the level of economic development and the structure of population are important statistics for regional or national policy-making. However, the collection of these characteristics usually depends on demographic and social surveys, which are time- and labor-intensive. To address these issues, we propose a machine learning-based approach to estimate and map the economic development from multi-source open available geospatial data, including remote sensing imagery and OpenStreetMap road networks. Specifically, we first extract knowledge-based features from different data sources; then the multi-view graphs are constructed through different perspectives of spatial adjacency and feature similarity; and a multi-view graph neural network (MVGNN) model is built on them and trained in a self-supervised learning manner. Then, the handcrafted features and the learned graph representations are combined to estimate the regional economic development indicators via random forest models. Taking China’s county-level gross domestic product (GDP) as an example, extensive experiments have been conducted and the results demonstrate the effectiveness of the proposed method, and the combination of the knowledge-based and learning-based features can significantly outperform baseline methods. Our proposed approach can advance the goal of acquiring timely and accurate socioeconomic variables through widely accessible geospatial data, which has the potential to extend to more social indicators and other geographic regions to support smart governance and policy-making in the future.\n                    </jats:p>	 @article{Cao_2022, title={MACHINE LEARNING-BASED ECONOMIC DEVELOPMENT MAPPING FROM MULTI-SOURCE OPEN GEOSPATIAL DATA}, volume={V-4–2022}, ISSN={2194-9050}, url={http://dx.doi.org/10.5194/isprs-annals-V-4-2022-259-2022}, DOI={10.5194/isprs-annals-v-4-2022-259-2022}, journal={ISPRS Annals of the Photogrammetry, Remote Sensing and Spatial Information Sciences}, publisher={Copernicus GmbH}, author={Cao, R. and Tu, W. and Cai, J. and Zhao, T. and Xiao, J. and Cao, J. and Gao, Q. and Su, H.}, year={2022}, month=may, pages={259–266} }\n	2025-07-29 22:40:59.176	2025-07-29 22:40:59.176	\N	\N	\N	\N
36	ua7ebdwtqib7kk0ra7wbrq12	10.5194/isprs-annals-V-4-2022-259-2022	MACHINE LEARNING-BASED ECONOMIC DEVELOPMENT MAPPING FROM MULTI-SOURCE OPEN GEOSPATIAL DATA	R. Cao, W. Tu, J. Cai, T. Zhao, J. Xiao, J. Cao, Q. Gao, H. Su	2022	ISPRS Annals of the Photogrammetry, Remote Sensing and Spatial Information Sciences	<jats:p>Abstract. Timely and accurate socioeconomic indicators are the prerequisite for smart social governance. For example, the level of economic development and the structure of population are important statistics for regional or national policy-making. However, the collection of these characteristics usually depends on demographic and social surveys, which are time- and labor-intensive. To address these issues, we propose a machine learning-based approach to estimate and map the economic development from multi-source open available geospatial data, including remote sensing imagery and OpenStreetMap road networks. Specifically, we first extract knowledge-based features from different data sources; then the multi-view graphs are constructed through different perspectives of spatial adjacency and feature similarity; and a multi-view graph neural network (MVGNN) model is built on them and trained in a self-supervised learning manner. Then, the handcrafted features and the learned graph representations are combined to estimate the regional economic development indicators via random forest models. Taking China’s county-level gross domestic product (GDP) as an example, extensive experiments have been conducted and the results demonstrate the effectiveness of the proposed method, and the combination of the knowledge-based and learning-based features can significantly outperform baseline methods. Our proposed approach can advance the goal of acquiring timely and accurate socioeconomic variables through widely accessible geospatial data, which has the potential to extend to more social indicators and other geographic regions to support smart governance and policy-making in the future.\n                    </jats:p>	 @article{Cao_2022, title={MACHINE LEARNING-BASED ECONOMIC DEVELOPMENT MAPPING FROM MULTI-SOURCE OPEN GEOSPATIAL DATA}, volume={V-4–2022}, ISSN={2194-9050}, url={http://dx.doi.org/10.5194/isprs-annals-V-4-2022-259-2022}, DOI={10.5194/isprs-annals-v-4-2022-259-2022}, journal={ISPRS Annals of the Photogrammetry, Remote Sensing and Spatial Information Sciences}, publisher={Copernicus GmbH}, author={Cao, R. and Tu, W. and Cai, J. and Zhao, T. and Xiao, J. and Cao, J. and Gao, Q. and Su, H.}, year={2022}, month=may, pages={259–266} }\n	2025-07-29 22:40:59.176	2025-07-29 22:40:59.176	2025-07-29 22:40:59.209	\N	\N	\N
37	ppu1dkmowz1x4z7df1p8clx5	10.1109/JSTARS.2023.3301792	Exploring How Street-Level Images Help Enhance Remote-Sensing-Based Local Climate Zone Mapping	Cai Liao, Rui Cao, Qi-Li Gao, Jinzhou Cao, Nianxue Luo	2023	IEEE Journal of Selected Topics in Applied Earth Observations and Remote Sensing	\N	 @article{Liao_2023, title={Exploring How Street-Level Images Help Enhance Remote-Sensing-Based Local Climate Zone Mapping}, volume={16}, ISSN={2151-1535}, url={http://dx.doi.org/10.1109/JSTARS.2023.3301792}, DOI={10.1109/jstars.2023.3301792}, journal={IEEE Journal of Selected Topics in Applied Earth Observations and Remote Sensing}, publisher={Institute of Electrical and Electronics Engineers (IEEE)}, author={Liao, Cai and Cao, Rui and Gao, Qi-Li and Cao, Jinzhou and Luo, Nianxue}, year={2023}, pages={7662–7674} }\n	2025-07-29 22:41:32.894	2025-07-29 22:41:32.894	\N	\N	\N	\N
38	ppu1dkmowz1x4z7df1p8clx5	10.1109/JSTARS.2023.3301792	Exploring How Street-Level Images Help Enhance Remote-Sensing-Based Local Climate Zone Mapping	Cai Liao, Rui Cao, Qi-Li Gao, Jinzhou Cao, Nianxue Luo	2023	IEEE Journal of Selected Topics in Applied Earth Observations and Remote Sensing	\N	 @article{Liao_2023, title={Exploring How Street-Level Images Help Enhance Remote-Sensing-Based Local Climate Zone Mapping}, volume={16}, ISSN={2151-1535}, url={http://dx.doi.org/10.1109/JSTARS.2023.3301792}, DOI={10.1109/jstars.2023.3301792}, journal={IEEE Journal of Selected Topics in Applied Earth Observations and Remote Sensing}, publisher={Institute of Electrical and Electronics Engineers (IEEE)}, author={Liao, Cai and Cao, Rui and Gao, Qi-Li and Cao, Jinzhou and Luo, Nianxue}, year={2023}, pages={7662–7674} }\n	2025-07-29 22:41:32.894	2025-07-29 22:41:32.894	2025-07-29 22:41:32.907	\N	\N	\N
39	zpxidzs52r6uxxkbtxjohmyp	10.48550/arXiv.2208.04727	Responsible Urban Intelligence: Towards a Research Agenda	Cao, Rui, Gao, Qi-Li, Qiu, Guoping	2022	\N	Acceleration of urbanisation is posing great challenges to sustainable development. Growing accessibility to big data and artificial intelligence (AI) technologies have revolutionised many fields and offered great potential for addressing pressing urban problems. However, using these technologies without explicitly considering responsibilities would bring new societal and environmental issues. To maximise the benefits of big data and AI while minimising potential issues, we envisage a conceptual framework of Responsible Urban Intelligence (RUI) and advocate an agenda for action. We first define RUI as consisting of three major components including urban problems, enabling technologies, and responsibilities; then introduce transparency, fairness, and eco-friendliness as the three dimensions of responsibilities which naturally link with the human, space, and time dimensions of cities; and further develop a four-stage implementation framework for responsibilities as consisting of solution design, data preparation, model building, and practical application; and finally present a research agenda for RUI addressing challenging issues including data and model transparency, tension between performance and fairness, and solving urban problems in an eco-friendly manner.	@article{https://doi.org/10.48550/arxiv.2208.04727,\n  doi = {10.48550/ARXIV.2208.04727},\n  url = {https://arxiv.org/abs/2208.04727},\n  author = {Cao, Rui and Gao, Qi-Li and Qiu, Guoping},\n  keywords = {Computers and Society (cs.CY), FOS: Computer and information sciences, FOS: Computer and information sciences},\n  title = {Responsible Urban Intelligence: Towards a Research Agenda},\n  publisher = {arXiv},\n  year = {2022},\n  copyright = {arXiv.org perpetual, non-exclusive license}\n}\n	2025-07-29 22:42:08.622	2025-07-29 22:42:08.622	\N	\N	\N	\N
40	zpxidzs52r6uxxkbtxjohmyp	10.48550/arXiv.2208.04727	Responsible Urban Intelligence: Towards a Research Agenda	Cao, Rui, Gao, Qi-Li, Qiu, Guoping	2022	\N	Acceleration of urbanisation is posing great challenges to sustainable development. Growing accessibility to big data and artificial intelligence (AI) technologies have revolutionised many fields and offered great potential for addressing pressing urban problems. However, using these technologies without explicitly considering responsibilities would bring new societal and environmental issues. To maximise the benefits of big data and AI while minimising potential issues, we envisage a conceptual framework of Responsible Urban Intelligence (RUI) and advocate an agenda for action. We first define RUI as consisting of three major components including urban problems, enabling technologies, and responsibilities; then introduce transparency, fairness, and eco-friendliness as the three dimensions of responsibilities which naturally link with the human, space, and time dimensions of cities; and further develop a four-stage implementation framework for responsibilities as consisting of solution design, data preparation, model building, and practical application; and finally present a research agenda for RUI addressing challenging issues including data and model transparency, tension between performance and fairness, and solving urban problems in an eco-friendly manner.	@article{https://doi.org/10.48550/arxiv.2208.04727,\n  doi = {10.48550/ARXIV.2208.04727},\n  url = {https://arxiv.org/abs/2208.04727},\n  author = {Cao, Rui and Gao, Qi-Li and Qiu, Guoping},\n  keywords = {Computers and Society (cs.CY), FOS: Computer and information sciences, FOS: Computer and information sciences},\n  title = {Responsible Urban Intelligence: Towards a Research Agenda},\n  publisher = {arXiv},\n  year = {2022},\n  copyright = {arXiv.org perpetual, non-exclusive license}\n}\n	2025-07-29 22:42:08.622	2025-07-29 22:42:08.622	2025-07-29 22:42:08.641	\N	\N	\N
43	iwzy6foqslxi7e4v60vwn2ap	10.1177/23998083241234137	Unpacking urban scaling and socio-spatial inequalities in mobility: Evidence from England	Qi-Li Gao, Chen Zhong, Yikang Wang	2024	Environment and Planning B: Urban Analytics and City Science	<jats:p>Prior research on the scaling of city size and inequality has a primary focus on economic factors such as income. Limited research has addressed socio-spatial disparities in mobility, involving physical activities and social interactions among individuals and population groups. Utilising mobile phone app data, this study measured inequalities using multiple mobility-related indicators (i.e. the number of activity points, the radius of gyration, self-containment, and social interaction indices) and related to population size by scaling models. In England’s context, these indicators unfolding mobility patterns and social issues display different scaling regimes, varying from sublinear to super-linear. It was observed that larger cities are associated with greater social interactions, particularly among socioeconomically advantaged groups; however, they also exhibit exacerbated self-segregation. Due to the radiation effect of big cities, the performances (e.g. travel radius) of small surrounding towns deviate from the predicted values of scaling models. Within cities, the evenness of indicators is independent of population size and produces distinct spatial patterns. The findings expand upon previous research and provide a more nuanced understanding of the complex relationship between city size, urban inequality, and human mobility.</jats:p>	 @article{Gao_2024, title={Unpacking urban scaling and socio-spatial inequalities in mobility: Evidence from England}, volume={51}, ISSN={2399-8091}, url={http://dx.doi.org/10.1177/23998083241234137}, DOI={10.1177/23998083241234137}, number={7}, journal={Environment and Planning B: Urban Analytics and City Science}, publisher={SAGE Publications}, author={Gao, Qi-Li and Zhong, Chen and Wang, Yikang}, year={2024}, month=feb, pages={1531–1547} }\n	2025-07-29 22:43:17.147	2025-07-29 22:43:17.147	\N	\N	\N	\N
41	q97y0anx6s401kvosh4mfaat	10.11947/j.AGCS.2021.20200310	大数据驱动的城市活动空间动态研究	高琦丽	2021	测绘学报	\N	@article{高琦丽2021大数据驱动的城市活动空间动态研究,\n  title={大数据驱动的城市活动空间动态研究},\n  author={高琦丽},\n  journal={测绘学报},\n  volume={50},\n  number={6},\n  pages={850--850},\n  year={2021}\n}\n	2025-07-29 22:42:58.22	2025-07-29 22:48:38.371	\N	\N	1	\N
44	iwzy6foqslxi7e4v60vwn2ap	10.1177/23998083241234137	Unpacking urban scaling and socio-spatial inequalities in mobility: Evidence from England	Qi-Li Gao, Chen Zhong, Yikang Wang	2024	Environment and Planning B: Urban Analytics and City Science	<jats:p>Prior research on the scaling of city size and inequality has a primary focus on economic factors such as income. Limited research has addressed socio-spatial disparities in mobility, involving physical activities and social interactions among individuals and population groups. Utilising mobile phone app data, this study measured inequalities using multiple mobility-related indicators (i.e. the number of activity points, the radius of gyration, self-containment, and social interaction indices) and related to population size by scaling models. In England’s context, these indicators unfolding mobility patterns and social issues display different scaling regimes, varying from sublinear to super-linear. It was observed that larger cities are associated with greater social interactions, particularly among socioeconomically advantaged groups; however, they also exhibit exacerbated self-segregation. Due to the radiation effect of big cities, the performances (e.g. travel radius) of small surrounding towns deviate from the predicted values of scaling models. Within cities, the evenness of indicators is independent of population size and produces distinct spatial patterns. The findings expand upon previous research and provide a more nuanced understanding of the complex relationship between city size, urban inequality, and human mobility.</jats:p>	 @article{Gao_2024, title={Unpacking urban scaling and socio-spatial inequalities in mobility: Evidence from England}, volume={51}, ISSN={2399-8091}, url={http://dx.doi.org/10.1177/23998083241234137}, DOI={10.1177/23998083241234137}, number={7}, journal={Environment and Planning B: Urban Analytics and City Science}, publisher={SAGE Publications}, author={Gao, Qi-Li and Zhong, Chen and Wang, Yikang}, year={2024}, month=feb, pages={1531–1547} }\n	2025-07-29 22:43:17.147	2025-07-29 22:43:17.147	2025-07-29 22:43:17.161	\N	\N	\N
45	gllpds0dhzapznvyb3hna31h	10.1016/j.tbs.2025.101101	Measuring transit areas of influence via amenity change	Jin Zeng, Yang Yue, Qi-Li Gao	2025	Travel Behaviour and Society	\N	 @article{Zeng_2025, title={Measuring transit areas of influence via amenity change}, volume={41}, ISSN={2214-367X}, url={http://dx.doi.org/10.1016/j.tbs.2025.101101}, DOI={10.1016/j.tbs.2025.101101}, journal={Travel Behaviour and Society}, publisher={Elsevier BV}, author={Zeng, Jin and Yue, Yang and Gao, Qi-Li}, year={2025}, month=oct, pages={101101} }\n	2025-07-29 22:44:33.627	2025-07-29 22:44:33.627	\N	\N	\N	\N
48	rajjzdd2ymtoarlznz56vb3j	10.1016/j.apgeog.2025.103646	Exploring the associations of socioeconomic characteristics and distance decay effects with two-Steps spatial interaction model	Bowen Zhang, Chen Zhong, Qi-li Gao, Zahratu Shabrina	2025	Applied Geography	\N	 @article{Zhang_2025, title={Exploring the associations of socioeconomic characteristics and distance decay effects with two-Steps spatial interaction model}, volume={179}, ISSN={0143-6228}, url={http://dx.doi.org/10.1016/j.apgeog.2025.103646}, DOI={10.1016/j.apgeog.2025.103646}, journal={Applied Geography}, publisher={Elsevier BV}, author={Zhang, Bowen and Zhong, Chen and Gao, Qi-li and Shabrina, Zahratu}, year={2025}, month=jun, pages={103646} }\n	2025-07-29 22:44:54.425	2025-07-29 22:44:54.425	2025-07-29 22:44:54.438	\N	\N	\N
46	gllpds0dhzapznvyb3hna31h	10.1016/j.tbs.2025.101101	Measuring transit areas of influence via amenity change	Jin Zeng, Yang Yue, Qi-Li Gao	2025	Travel Behaviour and Society	\N	 @article{Zeng_2025, title={Measuring transit areas of influence via amenity change}, volume={41}, ISSN={2214-367X}, url={http://dx.doi.org/10.1016/j.tbs.2025.101101}, DOI={10.1016/j.tbs.2025.101101}, journal={Travel Behaviour and Society}, publisher={Elsevier BV}, author={Zeng, Jin and Yue, Yang and Gao, Qi-Li}, year={2025}, month=oct, pages={101101} }\n	2025-07-29 22:44:33.627	2025-07-29 22:44:33.627	2025-07-29 22:44:33.641	\N	\N	\N
47	rajjzdd2ymtoarlznz56vb3j	10.1016/j.apgeog.2025.103646	Exploring the associations of socioeconomic characteristics and distance decay effects with two-Steps spatial interaction model	Bowen Zhang, Chen Zhong, Qi-li Gao, Zahratu Shabrina	2025	Applied Geography	\N	 @article{Zhang_2025, title={Exploring the associations of socioeconomic characteristics and distance decay effects with two-Steps spatial interaction model}, volume={179}, ISSN={0143-6228}, url={http://dx.doi.org/10.1016/j.apgeog.2025.103646}, DOI={10.1016/j.apgeog.2025.103646}, journal={Applied Geography}, publisher={Elsevier BV}, author={Zhang, Bowen and Zhong, Chen and Gao, Qi-li and Shabrina, Zahratu}, year={2025}, month=jun, pages={103646} }\n	2025-07-29 22:44:54.425	2025-07-29 22:44:54.425	\N	\N	\N	\N
51	q97y0anx6s401kvosh4mfaat	10.11947/j.AGCS.2021.20200310	大数据驱动的城市活动空间动态研究	高琦丽	2021	测绘学报	\N	@article{高琦丽2021大数据驱动的城市活动空间动态研究,\n  title={大数据驱动的城市活动空间动态研究},\n  author={高琦丽},\n  journal={测绘学报},\n  volume={50},\n  number={6},\n  pages={850--850},\n  year={2021}\n}\n	2025-07-29 22:42:58.22	2025-07-29 22:48:38.371	2025-07-29 22:48:38.399	\N	1	\N
19	fbcgan1ehgkn96x02tysuvjn	10.13203/j.whugis20200535	多重解析地址选择页面	涂伟,曹劲舟,高琦丽,曹瑞,方志祥,乐阳,李清泉	2020	武汉大学学报	\N	@Article{1671-8860(2020)12-1875-09,\ntitle = {Sensing Urban Dynamics by Fusing Multi-sourced Spatiotemporal Big Data},\njournal = {Geomatics and Information Science of Wuhan University},\nvolume = {45},\nnumber = {12},\npages = {1875-1883},\nyear = {2020},\nissn = {1671-8860},\ndoi = {10.13203/j.whugis20200535},\t\nurl = {http://ch.whu.edu.cn/en/article/doi/10.13203/j.whugis20200535},\nauthor = {TU Wei and CAO Jinzhou and GAO Qili and CAO Rui and FANG Zhixiang and YUE Yang and LI Qingquan}\n}	2025-07-29 22:30:38.677	2025-07-29 22:48:55.242	\N	\N	1	\N
52	fbcgan1ehgkn96x02tysuvjn	10.13203/j.whugis20200535	多重解析地址选择页面	涂伟,曹劲舟,高琦丽,曹瑞,方志祥,乐阳,李清泉	2020	武汉大学学报	\N	@Article{1671-8860(2020)12-1875-09,\ntitle = {Sensing Urban Dynamics by Fusing Multi-sourced Spatiotemporal Big Data},\njournal = {Geomatics and Information Science of Wuhan University},\nvolume = {45},\nnumber = {12},\npages = {1875-1883},\nyear = {2020},\nissn = {1671-8860},\ndoi = {10.13203/j.whugis20200535},\t\nurl = {http://ch.whu.edu.cn/en/article/doi/10.13203/j.whugis20200535},\nauthor = {TU Wei and CAO Jinzhou and GAO Qili and CAO Rui and FANG Zhixiang and YUE Yang and LI Qingquan}\n}	2025-07-29 22:30:38.677	2025-07-29 22:48:55.242	2025-07-29 22:48:55.255	\N	1	\N
49	nfmfwkt0iyhyyyfatr4gpjkz	10.13813/j.cn11-5141/u.2022.0410	融入城市空间因素的通勤异质性多层解析	梁宇豪, 高琦丽, 郭莉, 乐阳	2022	城市交通	\N	@article{Liang2022融入,\n  title={融入城市空间因素的通勤异质性多层解析},\n  author={梁, 宇豪 and 高, 琦丽 and 郭, 莉 and 乐, 阳},\n  journal={城市交通},\n  volume={20},\n  issue={4},\n  pages={111-119},\n  year={2022}\n}	2025-07-29 22:45:45.803	2025-07-29 22:50:01.632	\N	\N	1	\N
53	nfmfwkt0iyhyyyfatr4gpjkz	10.13813/j.cn11-5141/u.2022.0410	融入城市空间因素的通勤异质性多层解析	梁宇豪, 高琦丽, 郭莉, 乐阳	2022	城市交通	\N	@article{Liang2022融入,\n  title={融入城市空间因素的通勤异质性多层解析},\n  author={梁, 宇豪 and 高, 琦丽 and 郭, 莉 and 乐, 阳},\n  journal={城市交通},\n  volume={20},\n  issue={4},\n  pages={111-119},\n  year={2022}\n}	2025-07-29 22:45:45.803	2025-07-29 22:50:01.632	2025-07-29 22:50:01.648	\N	1	\N
54	s1py0z4b95ozhtqxtiv1fvvd	10.1007/s43762-025-00190-0	Shaping future sustainable cities with AI-powered urban informatics: Toward human-AI symbiosis	Yang Yue, Guanyu Yan, Tian Lan, Rui Cao, Qili Gao, Wenxiu Gao, Bo Huang, Guan Huang, Zhengdong Huang, Zihan Kan, Xiang Li, Dong Liu, Xintao Liu, Ding Ma, Lili Wang, Jizhe Xia, Xiaochun Yang, Meng Zhou, Anthony Gar-On Yeh, Renzhong Guo, Christophe Claramunt	2025	Computational Urban Science	<jats:title>Abstract</jats:title>\n          <jats:p>The rapid evolution of Artificial Intelligence (AI) has ushered in a transformative era for urban studies, moving beyond traditional analytical methods to advanced Deep Learning architectures, with Transformers model in the spotlight. Yet, unlike bioinformatics, which has successfully utilised AI to decode static biological systems, or cheminformatics, which optimises chemical synthesis, urban informatics grappled with human-centric complexity that encompass subjective perceptions, socio-political dynamics, and multifaceted challenges that defy deterministic solutions. To avoid techno-solutionist pitfalls, we convened an interdisciplinary group of scholars to explore AI-powered urban informatics and proposed a Human-AI Symbiosis framework to foster sustainable cities and advance urban research. This Opinion paper synthesises insights into four key research directions, focusing on the evolving landscape of urban informatics and its potential to drive innovation in sustainable cities, policy-making, and societal development.</jats:p>	 @article{Yue_2025, title={Shaping future sustainable cities with AI-powered urban informatics: Toward human-AI symbiosis}, volume={5}, ISSN={2730-6852}, url={http://dx.doi.org/10.1007/s43762-025-00190-0}, DOI={10.1007/s43762-025-00190-0}, number={1}, journal={Computational Urban Science}, publisher={Springer Science and Business Media LLC}, author={Yue, Yang and Yan, Guanyu and Lan, Tian and Cao, Rui and Gao, Qili and Gao, Wenxiu and Huang, Bo and Huang, Guan and Huang, Zhengdong and Kan, Zihan and Li, Xiang and Liu, Dong and Liu, Xintao and Ma, Ding and Wang, Lili and Xia, Jizhe and Yang, Xiaochun and Zhou, Meng and Yeh, Anthony Gar-On and Guo, Renzhong and Claramunt, Christophe}, year={2025}, month=jun }\n	2025-07-29 22:55:12.552	2025-07-29 22:55:12.552	\N	\N	\N	\N
55	s1py0z4b95ozhtqxtiv1fvvd	10.1007/s43762-025-00190-0	Shaping future sustainable cities with AI-powered urban informatics: Toward human-AI symbiosis	Yang Yue, Guanyu Yan, Tian Lan, Rui Cao, Qili Gao, Wenxiu Gao, Bo Huang, Guan Huang, Zhengdong Huang, Zihan Kan, Xiang Li, Dong Liu, Xintao Liu, Ding Ma, Lili Wang, Jizhe Xia, Xiaochun Yang, Meng Zhou, Anthony Gar-On Yeh, Renzhong Guo, Christophe Claramunt	2025	Computational Urban Science	<jats:title>Abstract</jats:title>\n          <jats:p>The rapid evolution of Artificial Intelligence (AI) has ushered in a transformative era for urban studies, moving beyond traditional analytical methods to advanced Deep Learning architectures, with Transformers model in the spotlight. Yet, unlike bioinformatics, which has successfully utilised AI to decode static biological systems, or cheminformatics, which optimises chemical synthesis, urban informatics grappled with human-centric complexity that encompass subjective perceptions, socio-political dynamics, and multifaceted challenges that defy deterministic solutions. To avoid techno-solutionist pitfalls, we convened an interdisciplinary group of scholars to explore AI-powered urban informatics and proposed a Human-AI Symbiosis framework to foster sustainable cities and advance urban research. This Opinion paper synthesises insights into four key research directions, focusing on the evolving landscape of urban informatics and its potential to drive innovation in sustainable cities, policy-making, and societal development.</jats:p>	 @article{Yue_2025, title={Shaping future sustainable cities with AI-powered urban informatics: Toward human-AI symbiosis}, volume={5}, ISSN={2730-6852}, url={http://dx.doi.org/10.1007/s43762-025-00190-0}, DOI={10.1007/s43762-025-00190-0}, number={1}, journal={Computational Urban Science}, publisher={Springer Science and Business Media LLC}, author={Yue, Yang and Yan, Guanyu and Lan, Tian and Cao, Rui and Gao, Qili and Gao, Wenxiu and Huang, Bo and Huang, Guan and Huang, Zhengdong and Kan, Zihan and Li, Xiang and Liu, Dong and Liu, Xintao and Ma, Ding and Wang, Lili and Xia, Jizhe and Yang, Xiaochun and Zhou, Meng and Yeh, Anthony Gar-On and Guo, Renzhong and Claramunt, Christophe}, year={2025}, month=jun }\n	2025-07-29 22:55:12.552	2025-07-29 22:55:12.552	2025-07-29 22:55:12.565	\N	\N	\N
\.


--
-- Data for Name: strapi_api_token_permissions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.strapi_api_token_permissions (id, document_id, action, created_at, updated_at, published_at, created_by_id, updated_by_id, locale) FROM stdin;
\.


--
-- Data for Name: strapi_api_token_permissions_token_lnk; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.strapi_api_token_permissions_token_lnk (id, api_token_permission_id, api_token_id, api_token_permission_ord) FROM stdin;
\.


--
-- Data for Name: strapi_api_tokens; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.strapi_api_tokens (id, document_id, name, description, type, access_key, encrypted_key, last_used_at, expires_at, lifespan, created_at, updated_at, published_at, created_by_id, updated_by_id, locale) FROM stdin;
1	fw08j1fcwrxqxljdocx28yx0	Read Only	A default API token with read-only permissions, only used for accessing resources	read-only	767f173ed880915832bc167cae9ec898bd32b37db88966303e744d4892870cf6f97587178a1b6748625d0321e095aa8f085a3358b5ce3c23fcecc59128352034	\N	\N	\N	\N	2025-07-20 15:48:26.518	2025-07-20 15:48:26.518	2025-07-20 15:48:26.518	\N	\N	\N
2	mabha1d4fhgw8vejn4szhzzv	Full Access	A default API token with full access permissions, used for accessing or modifying resources	full-access	408288016d6dc77f61f072e1e234aa0bffb07fdbb5423395aa646a1b85562ec58c1f5eb3db33703b5ff59ebf1eacb2eabefe2250b54fad05fad302029fc14ffb	\N	\N	\N	\N	2025-07-20 15:48:26.535	2025-07-20 15:48:26.535	2025-07-20 15:48:26.535	\N	\N	\N
\.


--
-- Data for Name: strapi_core_store_settings; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.strapi_core_store_settings (id, key, value, type, environment, tag) FROM stdin;
2	plugin_content_manager_configuration_content_types::plugin::upload.file	{"settings":{"bulkable":true,"filterable":true,"searchable":true,"pageSize":10,"mainField":"name","defaultSortBy":"name","defaultSortOrder":"ASC"},"metadatas":{"id":{"edit":{},"list":{"label":"id","searchable":true,"sortable":true}},"name":{"edit":{"label":"name","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"name","searchable":true,"sortable":true}},"alternativeText":{"edit":{"label":"alternativeText","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"alternativeText","searchable":true,"sortable":true}},"caption":{"edit":{"label":"caption","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"caption","searchable":true,"sortable":true}},"width":{"edit":{"label":"width","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"width","searchable":true,"sortable":true}},"height":{"edit":{"label":"height","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"height","searchable":true,"sortable":true}},"formats":{"edit":{"label":"formats","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"formats","searchable":false,"sortable":false}},"hash":{"edit":{"label":"hash","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"hash","searchable":true,"sortable":true}},"ext":{"edit":{"label":"ext","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"ext","searchable":true,"sortable":true}},"mime":{"edit":{"label":"mime","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"mime","searchable":true,"sortable":true}},"size":{"edit":{"label":"size","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"size","searchable":true,"sortable":true}},"url":{"edit":{"label":"url","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"url","searchable":true,"sortable":true}},"previewUrl":{"edit":{"label":"previewUrl","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"previewUrl","searchable":true,"sortable":true}},"provider":{"edit":{"label":"provider","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"provider","searchable":true,"sortable":true}},"provider_metadata":{"edit":{"label":"provider_metadata","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"provider_metadata","searchable":false,"sortable":false}},"folder":{"edit":{"label":"folder","description":"","placeholder":"","visible":true,"editable":true,"mainField":"name"},"list":{"label":"folder","searchable":true,"sortable":true}},"folderPath":{"edit":{"label":"folderPath","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"folderPath","searchable":true,"sortable":true}},"createdAt":{"edit":{"label":"createdAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"createdAt","searchable":true,"sortable":true}},"updatedAt":{"edit":{"label":"updatedAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"updatedAt","searchable":true,"sortable":true}},"createdBy":{"edit":{"label":"createdBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"createdBy","searchable":true,"sortable":true}},"updatedBy":{"edit":{"label":"updatedBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"updatedBy","searchable":true,"sortable":true}},"documentId":{"edit":{},"list":{"label":"documentId","searchable":true,"sortable":true}}},"layouts":{"list":["id","name","alternativeText","caption"],"edit":[[{"name":"name","size":6},{"name":"alternativeText","size":6}],[{"name":"caption","size":6},{"name":"width","size":4}],[{"name":"height","size":4}],[{"name":"formats","size":12}],[{"name":"hash","size":6},{"name":"ext","size":6}],[{"name":"mime","size":6},{"name":"size","size":4}],[{"name":"url","size":6},{"name":"previewUrl","size":6}],[{"name":"provider","size":6}],[{"name":"provider_metadata","size":12}],[{"name":"folder","size":6},{"name":"folderPath","size":6}]]},"uid":"plugin::upload.file"}	object	\N	\N
3	plugin_content_manager_configuration_content_types::plugin::upload.folder	{"settings":{"bulkable":true,"filterable":true,"searchable":true,"pageSize":10,"mainField":"name","defaultSortBy":"name","defaultSortOrder":"ASC"},"metadatas":{"id":{"edit":{},"list":{"label":"id","searchable":true,"sortable":true}},"name":{"edit":{"label":"name","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"name","searchable":true,"sortable":true}},"pathId":{"edit":{"label":"pathId","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"pathId","searchable":true,"sortable":true}},"parent":{"edit":{"label":"parent","description":"","placeholder":"","visible":true,"editable":true,"mainField":"name"},"list":{"label":"parent","searchable":true,"sortable":true}},"children":{"edit":{"label":"children","description":"","placeholder":"","visible":true,"editable":true,"mainField":"name"},"list":{"label":"children","searchable":false,"sortable":false}},"files":{"edit":{"label":"files","description":"","placeholder":"","visible":true,"editable":true,"mainField":"name"},"list":{"label":"files","searchable":false,"sortable":false}},"path":{"edit":{"label":"path","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"path","searchable":true,"sortable":true}},"createdAt":{"edit":{"label":"createdAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"createdAt","searchable":true,"sortable":true}},"updatedAt":{"edit":{"label":"updatedAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"updatedAt","searchable":true,"sortable":true}},"createdBy":{"edit":{"label":"createdBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"createdBy","searchable":true,"sortable":true}},"updatedBy":{"edit":{"label":"updatedBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"updatedBy","searchable":true,"sortable":true}},"documentId":{"edit":{},"list":{"label":"documentId","searchable":true,"sortable":true}}},"layouts":{"list":["id","name","pathId","parent"],"edit":[[{"name":"name","size":6},{"name":"pathId","size":4}],[{"name":"parent","size":6},{"name":"children","size":6}],[{"name":"files","size":6},{"name":"path","size":6}]]},"uid":"plugin::upload.folder"}	object	\N	\N
4	plugin_content_manager_configuration_content_types::plugin::i18n.locale	{"settings":{"bulkable":true,"filterable":true,"searchable":true,"pageSize":10,"mainField":"name","defaultSortBy":"name","defaultSortOrder":"ASC"},"metadatas":{"id":{"edit":{},"list":{"label":"id","searchable":true,"sortable":true}},"name":{"edit":{"label":"name","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"name","searchable":true,"sortable":true}},"code":{"edit":{"label":"code","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"code","searchable":true,"sortable":true}},"createdAt":{"edit":{"label":"createdAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"createdAt","searchable":true,"sortable":true}},"updatedAt":{"edit":{"label":"updatedAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"updatedAt","searchable":true,"sortable":true}},"createdBy":{"edit":{"label":"createdBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"createdBy","searchable":true,"sortable":true}},"updatedBy":{"edit":{"label":"updatedBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"updatedBy","searchable":true,"sortable":true}},"documentId":{"edit":{},"list":{"label":"documentId","searchable":true,"sortable":true}}},"layouts":{"list":["id","name","code","createdAt"],"edit":[[{"name":"name","size":6},{"name":"code","size":6}]]},"uid":"plugin::i18n.locale"}	object	\N	\N
5	plugin_content_manager_configuration_content_types::plugin::content-releases.release	{"settings":{"bulkable":true,"filterable":true,"searchable":true,"pageSize":10,"mainField":"name","defaultSortBy":"name","defaultSortOrder":"ASC"},"metadatas":{"id":{"edit":{},"list":{"label":"id","searchable":true,"sortable":true}},"name":{"edit":{"label":"name","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"name","searchable":true,"sortable":true}},"releasedAt":{"edit":{"label":"releasedAt","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"releasedAt","searchable":true,"sortable":true}},"scheduledAt":{"edit":{"label":"scheduledAt","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"scheduledAt","searchable":true,"sortable":true}},"timezone":{"edit":{"label":"timezone","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"timezone","searchable":true,"sortable":true}},"status":{"edit":{"label":"status","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"status","searchable":true,"sortable":true}},"actions":{"edit":{"label":"actions","description":"","placeholder":"","visible":true,"editable":true,"mainField":"contentType"},"list":{"label":"actions","searchable":false,"sortable":false}},"createdAt":{"edit":{"label":"createdAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"createdAt","searchable":true,"sortable":true}},"updatedAt":{"edit":{"label":"updatedAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"updatedAt","searchable":true,"sortable":true}},"createdBy":{"edit":{"label":"createdBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"createdBy","searchable":true,"sortable":true}},"updatedBy":{"edit":{"label":"updatedBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"updatedBy","searchable":true,"sortable":true}},"documentId":{"edit":{},"list":{"label":"documentId","searchable":true,"sortable":true}}},"layouts":{"list":["id","name","releasedAt","scheduledAt"],"edit":[[{"name":"name","size":6},{"name":"releasedAt","size":6}],[{"name":"scheduledAt","size":6},{"name":"timezone","size":6}],[{"name":"status","size":6},{"name":"actions","size":6}]]},"uid":"plugin::content-releases.release"}	object	\N	\N
6	plugin_content_manager_configuration_content_types::plugin::content-releases.release-action	{"settings":{"bulkable":true,"filterable":true,"searchable":true,"pageSize":10,"mainField":"contentType","defaultSortBy":"contentType","defaultSortOrder":"ASC"},"metadatas":{"id":{"edit":{},"list":{"label":"id","searchable":true,"sortable":true}},"type":{"edit":{"label":"type","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"type","searchable":true,"sortable":true}},"contentType":{"edit":{"label":"contentType","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"contentType","searchable":true,"sortable":true}},"entryDocumentId":{"edit":{"label":"entryDocumentId","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"entryDocumentId","searchable":true,"sortable":true}},"release":{"edit":{"label":"release","description":"","placeholder":"","visible":true,"editable":true,"mainField":"name"},"list":{"label":"release","searchable":true,"sortable":true}},"isEntryValid":{"edit":{"label":"isEntryValid","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"isEntryValid","searchable":true,"sortable":true}},"createdAt":{"edit":{"label":"createdAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"createdAt","searchable":true,"sortable":true}},"updatedAt":{"edit":{"label":"updatedAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"updatedAt","searchable":true,"sortable":true}},"createdBy":{"edit":{"label":"createdBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"createdBy","searchable":true,"sortable":true}},"updatedBy":{"edit":{"label":"updatedBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"updatedBy","searchable":true,"sortable":true}},"documentId":{"edit":{},"list":{"label":"documentId","searchable":true,"sortable":true}}},"layouts":{"list":["id","type","contentType","entryDocumentId"],"edit":[[{"name":"type","size":6},{"name":"contentType","size":6}],[{"name":"entryDocumentId","size":6},{"name":"release","size":6}],[{"name":"isEntryValid","size":4}]]},"uid":"plugin::content-releases.release-action"}	object	\N	\N
7	plugin_content_manager_configuration_content_types::plugin::review-workflows.workflow	{"settings":{"bulkable":true,"filterable":true,"searchable":true,"pageSize":10,"mainField":"name","defaultSortBy":"name","defaultSortOrder":"ASC"},"metadatas":{"id":{"edit":{},"list":{"label":"id","searchable":true,"sortable":true}},"name":{"edit":{"label":"name","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"name","searchable":true,"sortable":true}},"stages":{"edit":{"label":"stages","description":"","placeholder":"","visible":true,"editable":true,"mainField":"name"},"list":{"label":"stages","searchable":false,"sortable":false}},"stageRequiredToPublish":{"edit":{"label":"stageRequiredToPublish","description":"","placeholder":"","visible":true,"editable":true,"mainField":"name"},"list":{"label":"stageRequiredToPublish","searchable":true,"sortable":true}},"contentTypes":{"edit":{"label":"contentTypes","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"contentTypes","searchable":false,"sortable":false}},"createdAt":{"edit":{"label":"createdAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"createdAt","searchable":true,"sortable":true}},"updatedAt":{"edit":{"label":"updatedAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"updatedAt","searchable":true,"sortable":true}},"createdBy":{"edit":{"label":"createdBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"createdBy","searchable":true,"sortable":true}},"updatedBy":{"edit":{"label":"updatedBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"updatedBy","searchable":true,"sortable":true}},"documentId":{"edit":{},"list":{"label":"documentId","searchable":true,"sortable":true}}},"layouts":{"list":["id","name","stages","stageRequiredToPublish"],"edit":[[{"name":"name","size":6},{"name":"stages","size":6}],[{"name":"stageRequiredToPublish","size":6}],[{"name":"contentTypes","size":12}]]},"uid":"plugin::review-workflows.workflow"}	object	\N	\N
8	plugin_content_manager_configuration_content_types::plugin::users-permissions.permission	{"settings":{"bulkable":true,"filterable":true,"searchable":true,"pageSize":10,"mainField":"action","defaultSortBy":"action","defaultSortOrder":"ASC"},"metadatas":{"id":{"edit":{},"list":{"label":"id","searchable":true,"sortable":true}},"action":{"edit":{"label":"action","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"action","searchable":true,"sortable":true}},"role":{"edit":{"label":"role","description":"","placeholder":"","visible":true,"editable":true,"mainField":"name"},"list":{"label":"role","searchable":true,"sortable":true}},"createdAt":{"edit":{"label":"createdAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"createdAt","searchable":true,"sortable":true}},"updatedAt":{"edit":{"label":"updatedAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"updatedAt","searchable":true,"sortable":true}},"createdBy":{"edit":{"label":"createdBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"createdBy","searchable":true,"sortable":true}},"updatedBy":{"edit":{"label":"updatedBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"updatedBy","searchable":true,"sortable":true}},"documentId":{"edit":{},"list":{"label":"documentId","searchable":true,"sortable":true}}},"layouts":{"list":["id","action","role","createdAt"],"edit":[[{"name":"action","size":6},{"name":"role","size":6}]]},"uid":"plugin::users-permissions.permission"}	object	\N	\N
9	plugin_content_manager_configuration_content_types::plugin::review-workflows.workflow-stage	{"settings":{"bulkable":true,"filterable":true,"searchable":true,"pageSize":10,"mainField":"name","defaultSortBy":"name","defaultSortOrder":"ASC"},"metadatas":{"id":{"edit":{},"list":{"label":"id","searchable":true,"sortable":true}},"name":{"edit":{"label":"name","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"name","searchable":true,"sortable":true}},"color":{"edit":{"label":"color","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"color","searchable":true,"sortable":true}},"workflow":{"edit":{"label":"workflow","description":"","placeholder":"","visible":true,"editable":true,"mainField":"name"},"list":{"label":"workflow","searchable":true,"sortable":true}},"permissions":{"edit":{"label":"permissions","description":"","placeholder":"","visible":true,"editable":true,"mainField":"action"},"list":{"label":"permissions","searchable":false,"sortable":false}},"createdAt":{"edit":{"label":"createdAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"createdAt","searchable":true,"sortable":true}},"updatedAt":{"edit":{"label":"updatedAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"updatedAt","searchable":true,"sortable":true}},"createdBy":{"edit":{"label":"createdBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"createdBy","searchable":true,"sortable":true}},"updatedBy":{"edit":{"label":"updatedBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"updatedBy","searchable":true,"sortable":true}},"documentId":{"edit":{},"list":{"label":"documentId","searchable":true,"sortable":true}}},"layouts":{"list":["id","name","color","workflow"],"edit":[[{"name":"name","size":6},{"name":"color","size":6}],[{"name":"workflow","size":6},{"name":"permissions","size":6}]]},"uid":"plugin::review-workflows.workflow-stage"}	object	\N	\N
1	strapi_content_types_schema	{"api::doi-collection.doi-collection":{"kind":"collectionType","collectionName":"doi_collections","info":{"singularName":"doi-collection","pluralName":"doi-collections","displayName":"DOI_collection"},"options":{"draftAndPublish":true},"pluginOptions":{},"attributes":{"doi":{"type":"string","required":true},"createdAt":{"type":"datetime"},"updatedAt":{"type":"datetime"},"publishedAt":{"type":"datetime","configurable":false,"writable":true,"visible":false},"createdBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"updatedBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"locale":{"writable":true,"private":true,"configurable":false,"visible":false,"type":"string"},"localizations":{"type":"relation","relation":"oneToMany","target":"api::doi-collection.doi-collection","writable":false,"private":true,"configurable":false,"visible":false,"unstable_virtual":true,"joinColumn":{"name":"document_id","referencedColumn":"document_id","referencedTable":"doi_collections"}}},"apiName":"doi-collection","globalId":"DoiCollection","uid":"api::doi-collection.doi-collection","modelType":"contentType","__schema__":{"collectionName":"doi_collections","info":{"singularName":"doi-collection","pluralName":"doi-collections","displayName":"DOI_collection"},"options":{"draftAndPublish":true},"pluginOptions":{},"attributes":{"doi":{"type":"string","required":true}},"kind":"collectionType"},"modelName":"doi-collection","actions":{},"lifecycles":{}},"api::paper-info.paper-info":{"kind":"collectionType","collectionName":"paper_infos","info":{"singularName":"paper-info","pluralName":"paper-infos","displayName":"paper_info"},"options":{"draftAndPublish":true},"pluginOptions":{},"attributes":{"doi":{"type":"string","required":true},"title":{"type":"string"},"authors":{"type":"text"},"year":{"type":"integer"},"journal":{"type":"string"},"abstract":{"type":"text"},"bibtex":{"type":"text"},"createdAt":{"type":"datetime"},"updatedAt":{"type":"datetime"},"publishedAt":{"type":"datetime","configurable":false,"writable":true,"visible":false},"createdBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"updatedBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"locale":{"writable":true,"private":true,"configurable":false,"visible":false,"type":"string"},"localizations":{"type":"relation","relation":"oneToMany","target":"api::paper-info.paper-info","writable":false,"private":true,"configurable":false,"visible":false,"unstable_virtual":true,"joinColumn":{"name":"document_id","referencedColumn":"document_id","referencedTable":"paper_infos"}}},"apiName":"paper-info","globalId":"PaperInfo","uid":"api::paper-info.paper-info","modelType":"contentType","__schema__":{"collectionName":"paper_infos","info":{"singularName":"paper-info","pluralName":"paper-infos","displayName":"paper_info"},"options":{"draftAndPublish":true},"pluginOptions":{},"attributes":{"doi":{"type":"string","required":true},"title":{"type":"string"},"authors":{"type":"text"},"year":{"type":"integer"},"journal":{"type":"string"},"abstract":{"type":"text"},"bibtex":{"type":"text"}},"kind":"collectionType"},"modelName":"paper-info","actions":{},"lifecycles":{}},"api::team-member.team-member":{"kind":"collectionType","collectionName":"team_members","info":{"singularName":"team-member","pluralName":"team-members","displayName":"team_member"},"options":{"draftAndPublish":true},"pluginOptions":{},"attributes":{"name":{"type":"string","required":true},"title":{"type":"string","required":true},"role":{"type":"enumeration","required":true,"enum":["Mentor","Collaborator","RA","Alumni","PhD","Master","Bachelor"]},"avatar":{"type":"media","multiple":true,"allowedTypes":["images"]},"researchDirection":{"type":"blocks","required":true},"email":{"type":"email","required":true},"phone":{"type":"string"},"website":{"type":"string"},"education":{"type":"blocks"},"bio":{"type":"blocks"},"enrollmentYear":{"type":"integer"},"graduationYear":{"type":"integer"},"company":{"type":"string"},"position":{"type":"string"},"sortOrder":{"type":"integer"},"createdAt":{"type":"datetime"},"updatedAt":{"type":"datetime"},"publishedAt":{"type":"datetime","configurable":false,"writable":true,"visible":false},"createdBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"updatedBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"locale":{"writable":true,"private":true,"configurable":false,"visible":false,"type":"string"},"localizations":{"type":"relation","relation":"oneToMany","target":"api::team-member.team-member","writable":false,"private":true,"configurable":false,"visible":false,"unstable_virtual":true,"joinColumn":{"name":"document_id","referencedColumn":"document_id","referencedTable":"team_members"}}},"apiName":"team-member","globalId":"TeamMember","uid":"api::team-member.team-member","modelType":"contentType","__schema__":{"collectionName":"team_members","info":{"singularName":"team-member","pluralName":"team-members","displayName":"team_member"},"options":{"draftAndPublish":true},"pluginOptions":{},"attributes":{"name":{"type":"string","required":true},"title":{"type":"string","required":true},"role":{"type":"enumeration","required":true,"enum":["Mentor","Collaborator","RA","Alumni","PhD","Master","Bachelor"]},"avatar":{"type":"media","multiple":true,"allowedTypes":["images"]},"researchDirection":{"type":"blocks","required":true},"email":{"type":"email","required":true},"phone":{"type":"string"},"website":{"type":"string"},"education":{"type":"blocks"},"bio":{"type":"blocks"},"enrollmentYear":{"type":"integer"},"graduationYear":{"type":"integer"},"company":{"type":"string"},"position":{"type":"string"},"sortOrder":{"type":"integer"}},"kind":"collectionType"},"modelName":"team-member","actions":{},"lifecycles":{}},"plugin::upload.file":{"collectionName":"files","info":{"singularName":"file","pluralName":"files","displayName":"File","description":""},"options":{"draftAndPublish":false},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"name":{"type":"string","configurable":false,"required":true},"alternativeText":{"type":"string","configurable":false},"caption":{"type":"string","configurable":false},"width":{"type":"integer","configurable":false},"height":{"type":"integer","configurable":false},"formats":{"type":"json","configurable":false},"hash":{"type":"string","configurable":false,"required":true},"ext":{"type":"string","configurable":false},"mime":{"type":"string","configurable":false,"required":true},"size":{"type":"decimal","configurable":false,"required":true},"url":{"type":"string","configurable":false,"required":true},"previewUrl":{"type":"string","configurable":false},"provider":{"type":"string","configurable":false,"required":true},"provider_metadata":{"type":"json","configurable":false},"related":{"type":"relation","relation":"morphToMany","configurable":false},"folder":{"type":"relation","relation":"manyToOne","target":"plugin::upload.folder","inversedBy":"files","private":true},"folderPath":{"type":"string","minLength":1,"required":true,"private":true,"searchable":false},"createdAt":{"type":"datetime"},"updatedAt":{"type":"datetime"},"publishedAt":{"type":"datetime","configurable":false,"writable":true,"visible":false},"createdBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"updatedBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"locale":{"writable":true,"private":true,"configurable":false,"visible":false,"type":"string"},"localizations":{"type":"relation","relation":"oneToMany","target":"plugin::upload.file","writable":false,"private":true,"configurable":false,"visible":false,"unstable_virtual":true,"joinColumn":{"name":"document_id","referencedColumn":"document_id","referencedTable":"files"}}},"indexes":[{"name":"upload_files_folder_path_index","columns":["folder_path"],"type":null},{"name":"upload_files_created_at_index","columns":["created_at"],"type":null},{"name":"upload_files_updated_at_index","columns":["updated_at"],"type":null},{"name":"upload_files_name_index","columns":["name"],"type":null},{"name":"upload_files_size_index","columns":["size"],"type":null},{"name":"upload_files_ext_index","columns":["ext"],"type":null}],"plugin":"upload","globalId":"UploadFile","uid":"plugin::upload.file","modelType":"contentType","kind":"collectionType","__schema__":{"collectionName":"files","info":{"singularName":"file","pluralName":"files","displayName":"File","description":""},"options":{},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"name":{"type":"string","configurable":false,"required":true},"alternativeText":{"type":"string","configurable":false},"caption":{"type":"string","configurable":false},"width":{"type":"integer","configurable":false},"height":{"type":"integer","configurable":false},"formats":{"type":"json","configurable":false},"hash":{"type":"string","configurable":false,"required":true},"ext":{"type":"string","configurable":false},"mime":{"type":"string","configurable":false,"required":true},"size":{"type":"decimal","configurable":false,"required":true},"url":{"type":"string","configurable":false,"required":true},"previewUrl":{"type":"string","configurable":false},"provider":{"type":"string","configurable":false,"required":true},"provider_metadata":{"type":"json","configurable":false},"related":{"type":"relation","relation":"morphToMany","configurable":false},"folder":{"type":"relation","relation":"manyToOne","target":"plugin::upload.folder","inversedBy":"files","private":true},"folderPath":{"type":"string","minLength":1,"required":true,"private":true,"searchable":false}},"kind":"collectionType"},"modelName":"file"},"plugin::upload.folder":{"collectionName":"upload_folders","info":{"singularName":"folder","pluralName":"folders","displayName":"Folder"},"options":{"draftAndPublish":false},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"name":{"type":"string","minLength":1,"required":true},"pathId":{"type":"integer","unique":true,"required":true},"parent":{"type":"relation","relation":"manyToOne","target":"plugin::upload.folder","inversedBy":"children"},"children":{"type":"relation","relation":"oneToMany","target":"plugin::upload.folder","mappedBy":"parent"},"files":{"type":"relation","relation":"oneToMany","target":"plugin::upload.file","mappedBy":"folder"},"path":{"type":"string","minLength":1,"required":true},"createdAt":{"type":"datetime"},"updatedAt":{"type":"datetime"},"publishedAt":{"type":"datetime","configurable":false,"writable":true,"visible":false},"createdBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"updatedBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"locale":{"writable":true,"private":true,"configurable":false,"visible":false,"type":"string"},"localizations":{"type":"relation","relation":"oneToMany","target":"plugin::upload.folder","writable":false,"private":true,"configurable":false,"visible":false,"unstable_virtual":true,"joinColumn":{"name":"document_id","referencedColumn":"document_id","referencedTable":"upload_folders"}}},"indexes":[{"name":"upload_folders_path_id_index","columns":["path_id"],"type":"unique"},{"name":"upload_folders_path_index","columns":["path"],"type":"unique"}],"plugin":"upload","globalId":"UploadFolder","uid":"plugin::upload.folder","modelType":"contentType","kind":"collectionType","__schema__":{"collectionName":"upload_folders","info":{"singularName":"folder","pluralName":"folders","displayName":"Folder"},"options":{},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"name":{"type":"string","minLength":1,"required":true},"pathId":{"type":"integer","unique":true,"required":true},"parent":{"type":"relation","relation":"manyToOne","target":"plugin::upload.folder","inversedBy":"children"},"children":{"type":"relation","relation":"oneToMany","target":"plugin::upload.folder","mappedBy":"parent"},"files":{"type":"relation","relation":"oneToMany","target":"plugin::upload.file","mappedBy":"folder"},"path":{"type":"string","minLength":1,"required":true}},"kind":"collectionType"},"modelName":"folder"},"plugin::i18n.locale":{"info":{"singularName":"locale","pluralName":"locales","collectionName":"locales","displayName":"Locale","description":""},"options":{"draftAndPublish":false},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"name":{"type":"string","min":1,"max":50,"configurable":false},"code":{"type":"string","unique":true,"configurable":false},"createdAt":{"type":"datetime"},"updatedAt":{"type":"datetime"},"publishedAt":{"type":"datetime","configurable":false,"writable":true,"visible":false},"createdBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"updatedBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"locale":{"writable":true,"private":true,"configurable":false,"visible":false,"type":"string"},"localizations":{"type":"relation","relation":"oneToMany","target":"plugin::i18n.locale","writable":false,"private":true,"configurable":false,"visible":false,"unstable_virtual":true,"joinColumn":{"name":"document_id","referencedColumn":"document_id","referencedTable":"i18n_locale"}}},"plugin":"i18n","collectionName":"i18n_locale","globalId":"I18NLocale","uid":"plugin::i18n.locale","modelType":"contentType","kind":"collectionType","__schema__":{"collectionName":"i18n_locale","info":{"singularName":"locale","pluralName":"locales","collectionName":"locales","displayName":"Locale","description":""},"options":{},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"name":{"type":"string","min":1,"max":50,"configurable":false},"code":{"type":"string","unique":true,"configurable":false}},"kind":"collectionType"},"modelName":"locale"},"plugin::content-releases.release":{"collectionName":"strapi_releases","info":{"singularName":"release","pluralName":"releases","displayName":"Release"},"options":{"draftAndPublish":false},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"name":{"type":"string","required":true},"releasedAt":{"type":"datetime"},"scheduledAt":{"type":"datetime"},"timezone":{"type":"string"},"status":{"type":"enumeration","enum":["ready","blocked","failed","done","empty"],"required":true},"actions":{"type":"relation","relation":"oneToMany","target":"plugin::content-releases.release-action","mappedBy":"release"},"createdAt":{"type":"datetime"},"updatedAt":{"type":"datetime"},"publishedAt":{"type":"datetime","configurable":false,"writable":true,"visible":false},"createdBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"updatedBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"locale":{"writable":true,"private":true,"configurable":false,"visible":false,"type":"string"},"localizations":{"type":"relation","relation":"oneToMany","target":"plugin::content-releases.release","writable":false,"private":true,"configurable":false,"visible":false,"unstable_virtual":true,"joinColumn":{"name":"document_id","referencedColumn":"document_id","referencedTable":"strapi_releases"}}},"plugin":"content-releases","globalId":"ContentReleasesRelease","uid":"plugin::content-releases.release","modelType":"contentType","kind":"collectionType","__schema__":{"collectionName":"strapi_releases","info":{"singularName":"release","pluralName":"releases","displayName":"Release"},"options":{"draftAndPublish":false},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"name":{"type":"string","required":true},"releasedAt":{"type":"datetime"},"scheduledAt":{"type":"datetime"},"timezone":{"type":"string"},"status":{"type":"enumeration","enum":["ready","blocked","failed","done","empty"],"required":true},"actions":{"type":"relation","relation":"oneToMany","target":"plugin::content-releases.release-action","mappedBy":"release"}},"kind":"collectionType"},"modelName":"release"},"plugin::content-releases.release-action":{"collectionName":"strapi_release_actions","info":{"singularName":"release-action","pluralName":"release-actions","displayName":"Release Action"},"options":{"draftAndPublish":false},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"type":{"type":"enumeration","enum":["publish","unpublish"],"required":true},"contentType":{"type":"string","required":true},"entryDocumentId":{"type":"string"},"locale":{"writable":true,"private":true,"configurable":false,"visible":false,"type":"string"},"release":{"type":"relation","relation":"manyToOne","target":"plugin::content-releases.release","inversedBy":"actions"},"isEntryValid":{"type":"boolean"},"createdAt":{"type":"datetime"},"updatedAt":{"type":"datetime"},"publishedAt":{"type":"datetime","configurable":false,"writable":true,"visible":false},"createdBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"updatedBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"localizations":{"type":"relation","relation":"oneToMany","target":"plugin::content-releases.release-action","writable":false,"private":true,"configurable":false,"visible":false,"unstable_virtual":true,"joinColumn":{"name":"document_id","referencedColumn":"document_id","referencedTable":"strapi_release_actions"}}},"plugin":"content-releases","globalId":"ContentReleasesReleaseAction","uid":"plugin::content-releases.release-action","modelType":"contentType","kind":"collectionType","__schema__":{"collectionName":"strapi_release_actions","info":{"singularName":"release-action","pluralName":"release-actions","displayName":"Release Action"},"options":{"draftAndPublish":false},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"type":{"type":"enumeration","enum":["publish","unpublish"],"required":true},"contentType":{"type":"string","required":true},"entryDocumentId":{"type":"string"},"locale":{"type":"string"},"release":{"type":"relation","relation":"manyToOne","target":"plugin::content-releases.release","inversedBy":"actions"},"isEntryValid":{"type":"boolean"}},"kind":"collectionType"},"modelName":"release-action"},"plugin::review-workflows.workflow":{"collectionName":"strapi_workflows","info":{"name":"Workflow","description":"","singularName":"workflow","pluralName":"workflows","displayName":"Workflow"},"options":{"draftAndPublish":false},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"name":{"type":"string","required":true,"unique":true},"stages":{"type":"relation","target":"plugin::review-workflows.workflow-stage","relation":"oneToMany","mappedBy":"workflow"},"stageRequiredToPublish":{"type":"relation","target":"plugin::review-workflows.workflow-stage","relation":"oneToOne","required":false},"contentTypes":{"type":"json","required":true,"default":"[]"},"createdAt":{"type":"datetime"},"updatedAt":{"type":"datetime"},"publishedAt":{"type":"datetime","configurable":false,"writable":true,"visible":false},"createdBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"updatedBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"locale":{"writable":true,"private":true,"configurable":false,"visible":false,"type":"string"},"localizations":{"type":"relation","relation":"oneToMany","target":"plugin::review-workflows.workflow","writable":false,"private":true,"configurable":false,"visible":false,"unstable_virtual":true,"joinColumn":{"name":"document_id","referencedColumn":"document_id","referencedTable":"strapi_workflows"}}},"plugin":"review-workflows","globalId":"ReviewWorkflowsWorkflow","uid":"plugin::review-workflows.workflow","modelType":"contentType","kind":"collectionType","__schema__":{"collectionName":"strapi_workflows","info":{"name":"Workflow","description":"","singularName":"workflow","pluralName":"workflows","displayName":"Workflow"},"options":{},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"name":{"type":"string","required":true,"unique":true},"stages":{"type":"relation","target":"plugin::review-workflows.workflow-stage","relation":"oneToMany","mappedBy":"workflow"},"stageRequiredToPublish":{"type":"relation","target":"plugin::review-workflows.workflow-stage","relation":"oneToOne","required":false},"contentTypes":{"type":"json","required":true,"default":"[]"}},"kind":"collectionType"},"modelName":"workflow"},"plugin::review-workflows.workflow-stage":{"collectionName":"strapi_workflows_stages","info":{"name":"Workflow Stage","description":"","singularName":"workflow-stage","pluralName":"workflow-stages","displayName":"Stages"},"options":{"version":"1.1.0","draftAndPublish":false},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"name":{"type":"string","configurable":false},"color":{"type":"string","configurable":false,"default":"#4945FF"},"workflow":{"type":"relation","target":"plugin::review-workflows.workflow","relation":"manyToOne","inversedBy":"stages","configurable":false},"permissions":{"type":"relation","target":"admin::permission","relation":"manyToMany","configurable":false},"createdAt":{"type":"datetime"},"updatedAt":{"type":"datetime"},"publishedAt":{"type":"datetime","configurable":false,"writable":true,"visible":false},"createdBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"updatedBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"locale":{"writable":true,"private":true,"configurable":false,"visible":false,"type":"string"},"localizations":{"type":"relation","relation":"oneToMany","target":"plugin::review-workflows.workflow-stage","writable":false,"private":true,"configurable":false,"visible":false,"unstable_virtual":true,"joinColumn":{"name":"document_id","referencedColumn":"document_id","referencedTable":"strapi_workflows_stages"}}},"plugin":"review-workflows","globalId":"ReviewWorkflowsWorkflowStage","uid":"plugin::review-workflows.workflow-stage","modelType":"contentType","kind":"collectionType","__schema__":{"collectionName":"strapi_workflows_stages","info":{"name":"Workflow Stage","description":"","singularName":"workflow-stage","pluralName":"workflow-stages","displayName":"Stages"},"options":{"version":"1.1.0"},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"name":{"type":"string","configurable":false},"color":{"type":"string","configurable":false,"default":"#4945FF"},"workflow":{"type":"relation","target":"plugin::review-workflows.workflow","relation":"manyToOne","inversedBy":"stages","configurable":false},"permissions":{"type":"relation","target":"admin::permission","relation":"manyToMany","configurable":false}},"kind":"collectionType"},"modelName":"workflow-stage"},"plugin::users-permissions.permission":{"collectionName":"up_permissions","info":{"name":"permission","description":"","singularName":"permission","pluralName":"permissions","displayName":"Permission"},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"action":{"type":"string","required":true,"configurable":false},"role":{"type":"relation","relation":"manyToOne","target":"plugin::users-permissions.role","inversedBy":"permissions","configurable":false},"createdAt":{"type":"datetime"},"updatedAt":{"type":"datetime"},"publishedAt":{"type":"datetime","configurable":false,"writable":true,"visible":false},"createdBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"updatedBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"locale":{"writable":true,"private":true,"configurable":false,"visible":false,"type":"string"},"localizations":{"type":"relation","relation":"oneToMany","target":"plugin::users-permissions.permission","writable":false,"private":true,"configurable":false,"visible":false,"unstable_virtual":true,"joinColumn":{"name":"document_id","referencedColumn":"document_id","referencedTable":"up_permissions"}}},"plugin":"users-permissions","globalId":"UsersPermissionsPermission","uid":"plugin::users-permissions.permission","modelType":"contentType","kind":"collectionType","__schema__":{"collectionName":"up_permissions","info":{"name":"permission","description":"","singularName":"permission","pluralName":"permissions","displayName":"Permission"},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"action":{"type":"string","required":true,"configurable":false},"role":{"type":"relation","relation":"manyToOne","target":"plugin::users-permissions.role","inversedBy":"permissions","configurable":false}},"kind":"collectionType"},"modelName":"permission","options":{"draftAndPublish":false}},"plugin::users-permissions.role":{"collectionName":"up_roles","info":{"name":"role","description":"","singularName":"role","pluralName":"roles","displayName":"Role"},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"name":{"type":"string","minLength":3,"required":true,"configurable":false},"description":{"type":"string","configurable":false},"type":{"type":"string","unique":true,"configurable":false},"permissions":{"type":"relation","relation":"oneToMany","target":"plugin::users-permissions.permission","mappedBy":"role","configurable":false},"users":{"type":"relation","relation":"oneToMany","target":"plugin::users-permissions.user","mappedBy":"role","configurable":false},"createdAt":{"type":"datetime"},"updatedAt":{"type":"datetime"},"publishedAt":{"type":"datetime","configurable":false,"writable":true,"visible":false},"createdBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"updatedBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"locale":{"writable":true,"private":true,"configurable":false,"visible":false,"type":"string"},"localizations":{"type":"relation","relation":"oneToMany","target":"plugin::users-permissions.role","writable":false,"private":true,"configurable":false,"visible":false,"unstable_virtual":true,"joinColumn":{"name":"document_id","referencedColumn":"document_id","referencedTable":"up_roles"}}},"plugin":"users-permissions","globalId":"UsersPermissionsRole","uid":"plugin::users-permissions.role","modelType":"contentType","kind":"collectionType","__schema__":{"collectionName":"up_roles","info":{"name":"role","description":"","singularName":"role","pluralName":"roles","displayName":"Role"},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"name":{"type":"string","minLength":3,"required":true,"configurable":false},"description":{"type":"string","configurable":false},"type":{"type":"string","unique":true,"configurable":false},"permissions":{"type":"relation","relation":"oneToMany","target":"plugin::users-permissions.permission","mappedBy":"role","configurable":false},"users":{"type":"relation","relation":"oneToMany","target":"plugin::users-permissions.user","mappedBy":"role","configurable":false}},"kind":"collectionType"},"modelName":"role","options":{"draftAndPublish":false}},"plugin::users-permissions.user":{"collectionName":"up_users","info":{"name":"user","description":"","singularName":"user","pluralName":"users","displayName":"User"},"options":{"timestamps":true,"draftAndPublish":false},"attributes":{"username":{"type":"string","minLength":3,"unique":true,"configurable":false,"required":true},"email":{"type":"email","minLength":6,"configurable":false,"required":true},"provider":{"type":"string","configurable":false},"password":{"type":"password","minLength":6,"configurable":false,"private":true,"searchable":false},"resetPasswordToken":{"type":"string","configurable":false,"private":true,"searchable":false},"confirmationToken":{"type":"string","configurable":false,"private":true,"searchable":false},"confirmed":{"type":"boolean","default":false,"configurable":false},"blocked":{"type":"boolean","default":false,"configurable":false},"role":{"type":"relation","relation":"manyToOne","target":"plugin::users-permissions.role","inversedBy":"users","configurable":false},"createdAt":{"type":"datetime"},"updatedAt":{"type":"datetime"},"publishedAt":{"type":"datetime","configurable":false,"writable":true,"visible":false},"createdBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"updatedBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"locale":{"writable":true,"private":true,"configurable":false,"visible":false,"type":"string"},"localizations":{"type":"relation","relation":"oneToMany","target":"plugin::users-permissions.user","writable":false,"private":true,"configurable":false,"visible":false,"unstable_virtual":true,"joinColumn":{"name":"document_id","referencedColumn":"document_id","referencedTable":"up_users"}}},"config":{"attributes":{"resetPasswordToken":{"hidden":true},"confirmationToken":{"hidden":true},"provider":{"hidden":true}}},"plugin":"users-permissions","globalId":"UsersPermissionsUser","uid":"plugin::users-permissions.user","modelType":"contentType","kind":"collectionType","__schema__":{"collectionName":"up_users","info":{"name":"user","description":"","singularName":"user","pluralName":"users","displayName":"User"},"options":{"timestamps":true},"attributes":{"username":{"type":"string","minLength":3,"unique":true,"configurable":false,"required":true},"email":{"type":"email","minLength":6,"configurable":false,"required":true},"provider":{"type":"string","configurable":false},"password":{"type":"password","minLength":6,"configurable":false,"private":true,"searchable":false},"resetPasswordToken":{"type":"string","configurable":false,"private":true,"searchable":false},"confirmationToken":{"type":"string","configurable":false,"private":true,"searchable":false},"confirmed":{"type":"boolean","default":false,"configurable":false},"blocked":{"type":"boolean","default":false,"configurable":false},"role":{"type":"relation","relation":"manyToOne","target":"plugin::users-permissions.role","inversedBy":"users","configurable":false}},"kind":"collectionType"},"modelName":"user"},"admin::permission":{"collectionName":"admin_permissions","info":{"name":"Permission","description":"","singularName":"permission","pluralName":"permissions","displayName":"Permission"},"options":{"draftAndPublish":false},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"action":{"type":"string","minLength":1,"configurable":false,"required":true},"actionParameters":{"type":"json","configurable":false,"required":false,"default":{}},"subject":{"type":"string","minLength":1,"configurable":false,"required":false},"properties":{"type":"json","configurable":false,"required":false,"default":{}},"conditions":{"type":"json","configurable":false,"required":false,"default":[]},"role":{"configurable":false,"type":"relation","relation":"manyToOne","inversedBy":"permissions","target":"admin::role"},"createdAt":{"type":"datetime"},"updatedAt":{"type":"datetime"},"publishedAt":{"type":"datetime","configurable":false,"writable":true,"visible":false},"createdBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"updatedBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"locale":{"writable":true,"private":true,"configurable":false,"visible":false,"type":"string"},"localizations":{"type":"relation","relation":"oneToMany","target":"admin::permission","writable":false,"private":true,"configurable":false,"visible":false,"unstable_virtual":true,"joinColumn":{"name":"document_id","referencedColumn":"document_id","referencedTable":"admin_permissions"}}},"plugin":"admin","globalId":"AdminPermission","uid":"admin::permission","modelType":"contentType","kind":"collectionType","__schema__":{"collectionName":"admin_permissions","info":{"name":"Permission","description":"","singularName":"permission","pluralName":"permissions","displayName":"Permission"},"options":{},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"action":{"type":"string","minLength":1,"configurable":false,"required":true},"actionParameters":{"type":"json","configurable":false,"required":false,"default":{}},"subject":{"type":"string","minLength":1,"configurable":false,"required":false},"properties":{"type":"json","configurable":false,"required":false,"default":{}},"conditions":{"type":"json","configurable":false,"required":false,"default":[]},"role":{"configurable":false,"type":"relation","relation":"manyToOne","inversedBy":"permissions","target":"admin::role"}},"kind":"collectionType"},"modelName":"permission"},"admin::user":{"collectionName":"admin_users","info":{"name":"User","description":"","singularName":"user","pluralName":"users","displayName":"User"},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"firstname":{"type":"string","unique":false,"minLength":1,"configurable":false,"required":false},"lastname":{"type":"string","unique":false,"minLength":1,"configurable":false,"required":false},"username":{"type":"string","unique":false,"configurable":false,"required":false},"email":{"type":"email","minLength":6,"configurable":false,"required":true,"unique":true,"private":true},"password":{"type":"password","minLength":6,"configurable":false,"required":false,"private":true,"searchable":false},"resetPasswordToken":{"type":"string","configurable":false,"private":true,"searchable":false},"registrationToken":{"type":"string","configurable":false,"private":true,"searchable":false},"isActive":{"type":"boolean","default":false,"configurable":false,"private":true},"roles":{"configurable":false,"private":true,"type":"relation","relation":"manyToMany","inversedBy":"users","target":"admin::role","collectionName":"strapi_users_roles"},"blocked":{"type":"boolean","default":false,"configurable":false,"private":true},"preferedLanguage":{"type":"string","configurable":false,"required":false,"searchable":false},"createdAt":{"type":"datetime"},"updatedAt":{"type":"datetime"},"publishedAt":{"type":"datetime","configurable":false,"writable":true,"visible":false},"createdBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"updatedBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"locale":{"writable":true,"private":true,"configurable":false,"visible":false,"type":"string"},"localizations":{"type":"relation","relation":"oneToMany","target":"admin::user","writable":false,"private":true,"configurable":false,"visible":false,"unstable_virtual":true,"joinColumn":{"name":"document_id","referencedColumn":"document_id","referencedTable":"admin_users"}}},"config":{"attributes":{"resetPasswordToken":{"hidden":true},"registrationToken":{"hidden":true}}},"plugin":"admin","globalId":"AdminUser","uid":"admin::user","modelType":"contentType","kind":"collectionType","__schema__":{"collectionName":"admin_users","info":{"name":"User","description":"","singularName":"user","pluralName":"users","displayName":"User"},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"firstname":{"type":"string","unique":false,"minLength":1,"configurable":false,"required":false},"lastname":{"type":"string","unique":false,"minLength":1,"configurable":false,"required":false},"username":{"type":"string","unique":false,"configurable":false,"required":false},"email":{"type":"email","minLength":6,"configurable":false,"required":true,"unique":true,"private":true},"password":{"type":"password","minLength":6,"configurable":false,"required":false,"private":true,"searchable":false},"resetPasswordToken":{"type":"string","configurable":false,"private":true,"searchable":false},"registrationToken":{"type":"string","configurable":false,"private":true,"searchable":false},"isActive":{"type":"boolean","default":false,"configurable":false,"private":true},"roles":{"configurable":false,"private":true,"type":"relation","relation":"manyToMany","inversedBy":"users","target":"admin::role","collectionName":"strapi_users_roles"},"blocked":{"type":"boolean","default":false,"configurable":false,"private":true},"preferedLanguage":{"type":"string","configurable":false,"required":false,"searchable":false}},"kind":"collectionType"},"modelName":"user","options":{"draftAndPublish":false}},"admin::role":{"collectionName":"admin_roles","info":{"name":"Role","description":"","singularName":"role","pluralName":"roles","displayName":"Role"},"options":{"draftAndPublish":false},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"name":{"type":"string","minLength":1,"unique":true,"configurable":false,"required":true},"code":{"type":"string","minLength":1,"unique":true,"configurable":false,"required":true},"description":{"type":"string","configurable":false},"users":{"configurable":false,"type":"relation","relation":"manyToMany","mappedBy":"roles","target":"admin::user"},"permissions":{"configurable":false,"type":"relation","relation":"oneToMany","mappedBy":"role","target":"admin::permission"},"createdAt":{"type":"datetime"},"updatedAt":{"type":"datetime"},"publishedAt":{"type":"datetime","configurable":false,"writable":true,"visible":false},"createdBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"updatedBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"locale":{"writable":true,"private":true,"configurable":false,"visible":false,"type":"string"},"localizations":{"type":"relation","relation":"oneToMany","target":"admin::role","writable":false,"private":true,"configurable":false,"visible":false,"unstable_virtual":true,"joinColumn":{"name":"document_id","referencedColumn":"document_id","referencedTable":"admin_roles"}}},"plugin":"admin","globalId":"AdminRole","uid":"admin::role","modelType":"contentType","kind":"collectionType","__schema__":{"collectionName":"admin_roles","info":{"name":"Role","description":"","singularName":"role","pluralName":"roles","displayName":"Role"},"options":{},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"name":{"type":"string","minLength":1,"unique":true,"configurable":false,"required":true},"code":{"type":"string","minLength":1,"unique":true,"configurable":false,"required":true},"description":{"type":"string","configurable":false},"users":{"configurable":false,"type":"relation","relation":"manyToMany","mappedBy":"roles","target":"admin::user"},"permissions":{"configurable":false,"type":"relation","relation":"oneToMany","mappedBy":"role","target":"admin::permission"}},"kind":"collectionType"},"modelName":"role"},"admin::api-token":{"collectionName":"strapi_api_tokens","info":{"name":"Api Token","singularName":"api-token","pluralName":"api-tokens","displayName":"Api Token","description":""},"options":{"draftAndPublish":false},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"name":{"type":"string","minLength":1,"configurable":false,"required":true,"unique":true},"description":{"type":"string","minLength":1,"configurable":false,"required":false,"default":""},"type":{"type":"enumeration","enum":["read-only","full-access","custom"],"configurable":false,"required":true,"default":"read-only"},"accessKey":{"type":"string","minLength":1,"configurable":false,"required":true,"searchable":false},"encryptedKey":{"type":"text","minLength":1,"configurable":false,"required":false,"searchable":false},"lastUsedAt":{"type":"datetime","configurable":false,"required":false},"permissions":{"type":"relation","target":"admin::api-token-permission","relation":"oneToMany","mappedBy":"token","configurable":false,"required":false},"expiresAt":{"type":"datetime","configurable":false,"required":false},"lifespan":{"type":"biginteger","configurable":false,"required":false},"createdAt":{"type":"datetime"},"updatedAt":{"type":"datetime"},"publishedAt":{"type":"datetime","configurable":false,"writable":true,"visible":false},"createdBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"updatedBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"locale":{"writable":true,"private":true,"configurable":false,"visible":false,"type":"string"},"localizations":{"type":"relation","relation":"oneToMany","target":"admin::api-token","writable":false,"private":true,"configurable":false,"visible":false,"unstable_virtual":true,"joinColumn":{"name":"document_id","referencedColumn":"document_id","referencedTable":"strapi_api_tokens"}}},"plugin":"admin","globalId":"AdminApiToken","uid":"admin::api-token","modelType":"contentType","kind":"collectionType","__schema__":{"collectionName":"strapi_api_tokens","info":{"name":"Api Token","singularName":"api-token","pluralName":"api-tokens","displayName":"Api Token","description":""},"options":{},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"name":{"type":"string","minLength":1,"configurable":false,"required":true,"unique":true},"description":{"type":"string","minLength":1,"configurable":false,"required":false,"default":""},"type":{"type":"enumeration","enum":["read-only","full-access","custom"],"configurable":false,"required":true,"default":"read-only"},"accessKey":{"type":"string","minLength":1,"configurable":false,"required":true,"searchable":false},"encryptedKey":{"type":"text","minLength":1,"configurable":false,"required":false,"searchable":false},"lastUsedAt":{"type":"datetime","configurable":false,"required":false},"permissions":{"type":"relation","target":"admin::api-token-permission","relation":"oneToMany","mappedBy":"token","configurable":false,"required":false},"expiresAt":{"type":"datetime","configurable":false,"required":false},"lifespan":{"type":"biginteger","configurable":false,"required":false}},"kind":"collectionType"},"modelName":"api-token"},"admin::api-token-permission":{"collectionName":"strapi_api_token_permissions","info":{"name":"API Token Permission","description":"","singularName":"api-token-permission","pluralName":"api-token-permissions","displayName":"API Token Permission"},"options":{"draftAndPublish":false},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"action":{"type":"string","minLength":1,"configurable":false,"required":true},"token":{"configurable":false,"type":"relation","relation":"manyToOne","inversedBy":"permissions","target":"admin::api-token"},"createdAt":{"type":"datetime"},"updatedAt":{"type":"datetime"},"publishedAt":{"type":"datetime","configurable":false,"writable":true,"visible":false},"createdBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"updatedBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"locale":{"writable":true,"private":true,"configurable":false,"visible":false,"type":"string"},"localizations":{"type":"relation","relation":"oneToMany","target":"admin::api-token-permission","writable":false,"private":true,"configurable":false,"visible":false,"unstable_virtual":true,"joinColumn":{"name":"document_id","referencedColumn":"document_id","referencedTable":"strapi_api_token_permissions"}}},"plugin":"admin","globalId":"AdminApiTokenPermission","uid":"admin::api-token-permission","modelType":"contentType","kind":"collectionType","__schema__":{"collectionName":"strapi_api_token_permissions","info":{"name":"API Token Permission","description":"","singularName":"api-token-permission","pluralName":"api-token-permissions","displayName":"API Token Permission"},"options":{},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"action":{"type":"string","minLength":1,"configurable":false,"required":true},"token":{"configurable":false,"type":"relation","relation":"manyToOne","inversedBy":"permissions","target":"admin::api-token"}},"kind":"collectionType"},"modelName":"api-token-permission"},"admin::transfer-token":{"collectionName":"strapi_transfer_tokens","info":{"name":"Transfer Token","singularName":"transfer-token","pluralName":"transfer-tokens","displayName":"Transfer Token","description":""},"options":{"draftAndPublish":false},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"name":{"type":"string","minLength":1,"configurable":false,"required":true,"unique":true},"description":{"type":"string","minLength":1,"configurable":false,"required":false,"default":""},"accessKey":{"type":"string","minLength":1,"configurable":false,"required":true},"lastUsedAt":{"type":"datetime","configurable":false,"required":false},"permissions":{"type":"relation","target":"admin::transfer-token-permission","relation":"oneToMany","mappedBy":"token","configurable":false,"required":false},"expiresAt":{"type":"datetime","configurable":false,"required":false},"lifespan":{"type":"biginteger","configurable":false,"required":false},"createdAt":{"type":"datetime"},"updatedAt":{"type":"datetime"},"publishedAt":{"type":"datetime","configurable":false,"writable":true,"visible":false},"createdBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"updatedBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"locale":{"writable":true,"private":true,"configurable":false,"visible":false,"type":"string"},"localizations":{"type":"relation","relation":"oneToMany","target":"admin::transfer-token","writable":false,"private":true,"configurable":false,"visible":false,"unstable_virtual":true,"joinColumn":{"name":"document_id","referencedColumn":"document_id","referencedTable":"strapi_transfer_tokens"}}},"plugin":"admin","globalId":"AdminTransferToken","uid":"admin::transfer-token","modelType":"contentType","kind":"collectionType","__schema__":{"collectionName":"strapi_transfer_tokens","info":{"name":"Transfer Token","singularName":"transfer-token","pluralName":"transfer-tokens","displayName":"Transfer Token","description":""},"options":{},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"name":{"type":"string","minLength":1,"configurable":false,"required":true,"unique":true},"description":{"type":"string","minLength":1,"configurable":false,"required":false,"default":""},"accessKey":{"type":"string","minLength":1,"configurable":false,"required":true},"lastUsedAt":{"type":"datetime","configurable":false,"required":false},"permissions":{"type":"relation","target":"admin::transfer-token-permission","relation":"oneToMany","mappedBy":"token","configurable":false,"required":false},"expiresAt":{"type":"datetime","configurable":false,"required":false},"lifespan":{"type":"biginteger","configurable":false,"required":false}},"kind":"collectionType"},"modelName":"transfer-token"},"admin::transfer-token-permission":{"collectionName":"strapi_transfer_token_permissions","info":{"name":"Transfer Token Permission","description":"","singularName":"transfer-token-permission","pluralName":"transfer-token-permissions","displayName":"Transfer Token Permission"},"options":{"draftAndPublish":false},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"action":{"type":"string","minLength":1,"configurable":false,"required":true},"token":{"configurable":false,"type":"relation","relation":"manyToOne","inversedBy":"permissions","target":"admin::transfer-token"},"createdAt":{"type":"datetime"},"updatedAt":{"type":"datetime"},"publishedAt":{"type":"datetime","configurable":false,"writable":true,"visible":false},"createdBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"updatedBy":{"type":"relation","relation":"oneToOne","target":"admin::user","configurable":false,"writable":false,"visible":false,"useJoinTable":false,"private":true},"locale":{"writable":true,"private":true,"configurable":false,"visible":false,"type":"string"},"localizations":{"type":"relation","relation":"oneToMany","target":"admin::transfer-token-permission","writable":false,"private":true,"configurable":false,"visible":false,"unstable_virtual":true,"joinColumn":{"name":"document_id","referencedColumn":"document_id","referencedTable":"strapi_transfer_token_permissions"}}},"plugin":"admin","globalId":"AdminTransferTokenPermission","uid":"admin::transfer-token-permission","modelType":"contentType","kind":"collectionType","__schema__":{"collectionName":"strapi_transfer_token_permissions","info":{"name":"Transfer Token Permission","description":"","singularName":"transfer-token-permission","pluralName":"transfer-token-permissions","displayName":"Transfer Token Permission"},"options":{},"pluginOptions":{"content-manager":{"visible":false},"content-type-builder":{"visible":false}},"attributes":{"action":{"type":"string","minLength":1,"configurable":false,"required":true},"token":{"configurable":false,"type":"relation","relation":"manyToOne","inversedBy":"permissions","target":"admin::transfer-token"}},"kind":"collectionType"},"modelName":"transfer-token-permission"}}	object	\N	\N
10	plugin_content_manager_configuration_content_types::plugin::users-permissions.role	{"settings":{"bulkable":true,"filterable":true,"searchable":true,"pageSize":10,"mainField":"name","defaultSortBy":"name","defaultSortOrder":"ASC"},"metadatas":{"id":{"edit":{},"list":{"label":"id","searchable":true,"sortable":true}},"name":{"edit":{"label":"name","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"name","searchable":true,"sortable":true}},"description":{"edit":{"label":"description","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"description","searchable":true,"sortable":true}},"type":{"edit":{"label":"type","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"type","searchable":true,"sortable":true}},"permissions":{"edit":{"label":"permissions","description":"","placeholder":"","visible":true,"editable":true,"mainField":"action"},"list":{"label":"permissions","searchable":false,"sortable":false}},"users":{"edit":{"label":"users","description":"","placeholder":"","visible":true,"editable":true,"mainField":"username"},"list":{"label":"users","searchable":false,"sortable":false}},"createdAt":{"edit":{"label":"createdAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"createdAt","searchable":true,"sortable":true}},"updatedAt":{"edit":{"label":"updatedAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"updatedAt","searchable":true,"sortable":true}},"createdBy":{"edit":{"label":"createdBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"createdBy","searchable":true,"sortable":true}},"updatedBy":{"edit":{"label":"updatedBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"updatedBy","searchable":true,"sortable":true}},"documentId":{"edit":{},"list":{"label":"documentId","searchable":true,"sortable":true}}},"layouts":{"list":["id","name","description","type"],"edit":[[{"name":"name","size":6},{"name":"description","size":6}],[{"name":"type","size":6},{"name":"permissions","size":6}],[{"name":"users","size":6}]]},"uid":"plugin::users-permissions.role"}	object	\N	\N
12	plugin_content_manager_configuration_content_types::plugin::users-permissions.user	{"settings":{"bulkable":true,"filterable":true,"searchable":true,"pageSize":10,"mainField":"username","defaultSortBy":"username","defaultSortOrder":"ASC"},"metadatas":{"id":{"edit":{},"list":{"label":"id","searchable":true,"sortable":true}},"username":{"edit":{"label":"username","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"username","searchable":true,"sortable":true}},"email":{"edit":{"label":"email","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"email","searchable":true,"sortable":true}},"provider":{"edit":{"label":"provider","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"provider","searchable":true,"sortable":true}},"password":{"edit":{"label":"password","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"password","searchable":true,"sortable":true}},"resetPasswordToken":{"edit":{"label":"resetPasswordToken","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"resetPasswordToken","searchable":true,"sortable":true}},"confirmationToken":{"edit":{"label":"confirmationToken","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"confirmationToken","searchable":true,"sortable":true}},"confirmed":{"edit":{"label":"confirmed","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"confirmed","searchable":true,"sortable":true}},"blocked":{"edit":{"label":"blocked","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"blocked","searchable":true,"sortable":true}},"role":{"edit":{"label":"role","description":"","placeholder":"","visible":true,"editable":true,"mainField":"name"},"list":{"label":"role","searchable":true,"sortable":true}},"createdAt":{"edit":{"label":"createdAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"createdAt","searchable":true,"sortable":true}},"updatedAt":{"edit":{"label":"updatedAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"updatedAt","searchable":true,"sortable":true}},"createdBy":{"edit":{"label":"createdBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"createdBy","searchable":true,"sortable":true}},"updatedBy":{"edit":{"label":"updatedBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"updatedBy","searchable":true,"sortable":true}},"documentId":{"edit":{},"list":{"label":"documentId","searchable":true,"sortable":true}}},"layouts":{"list":["id","username","email","confirmed"],"edit":[[{"name":"username","size":6},{"name":"email","size":6}],[{"name":"password","size":6},{"name":"confirmed","size":4}],[{"name":"blocked","size":4},{"name":"role","size":6}]]},"uid":"plugin::users-permissions.user"}	object	\N	\N
15	plugin_content_manager_configuration_content_types::admin::user	{"settings":{"bulkable":true,"filterable":true,"searchable":true,"pageSize":10,"mainField":"firstname","defaultSortBy":"firstname","defaultSortOrder":"ASC"},"metadatas":{"id":{"edit":{},"list":{"label":"id","searchable":true,"sortable":true}},"firstname":{"edit":{"label":"firstname","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"firstname","searchable":true,"sortable":true}},"lastname":{"edit":{"label":"lastname","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"lastname","searchable":true,"sortable":true}},"username":{"edit":{"label":"username","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"username","searchable":true,"sortable":true}},"email":{"edit":{"label":"email","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"email","searchable":true,"sortable":true}},"password":{"edit":{"label":"password","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"password","searchable":true,"sortable":true}},"resetPasswordToken":{"edit":{"label":"resetPasswordToken","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"resetPasswordToken","searchable":true,"sortable":true}},"registrationToken":{"edit":{"label":"registrationToken","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"registrationToken","searchable":true,"sortable":true}},"isActive":{"edit":{"label":"isActive","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"isActive","searchable":true,"sortable":true}},"roles":{"edit":{"label":"roles","description":"","placeholder":"","visible":true,"editable":true,"mainField":"name"},"list":{"label":"roles","searchable":false,"sortable":false}},"blocked":{"edit":{"label":"blocked","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"blocked","searchable":true,"sortable":true}},"preferedLanguage":{"edit":{"label":"preferedLanguage","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"preferedLanguage","searchable":true,"sortable":true}},"createdAt":{"edit":{"label":"createdAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"createdAt","searchable":true,"sortable":true}},"updatedAt":{"edit":{"label":"updatedAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"updatedAt","searchable":true,"sortable":true}},"createdBy":{"edit":{"label":"createdBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"createdBy","searchable":true,"sortable":true}},"updatedBy":{"edit":{"label":"updatedBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"updatedBy","searchable":true,"sortable":true}},"documentId":{"edit":{},"list":{"label":"documentId","searchable":true,"sortable":true}}},"layouts":{"list":["id","firstname","lastname","username"],"edit":[[{"name":"firstname","size":6},{"name":"lastname","size":6}],[{"name":"username","size":6},{"name":"email","size":6}],[{"name":"password","size":6},{"name":"isActive","size":4}],[{"name":"roles","size":6},{"name":"blocked","size":4}],[{"name":"preferedLanguage","size":6}]]},"uid":"admin::user"}	object	\N	\N
11	plugin_content_manager_configuration_content_types::admin::permission	{"settings":{"bulkable":true,"filterable":true,"searchable":true,"pageSize":10,"mainField":"action","defaultSortBy":"action","defaultSortOrder":"ASC"},"metadatas":{"id":{"edit":{},"list":{"label":"id","searchable":true,"sortable":true}},"action":{"edit":{"label":"action","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"action","searchable":true,"sortable":true}},"actionParameters":{"edit":{"label":"actionParameters","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"actionParameters","searchable":false,"sortable":false}},"subject":{"edit":{"label":"subject","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"subject","searchable":true,"sortable":true}},"properties":{"edit":{"label":"properties","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"properties","searchable":false,"sortable":false}},"conditions":{"edit":{"label":"conditions","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"conditions","searchable":false,"sortable":false}},"role":{"edit":{"label":"role","description":"","placeholder":"","visible":true,"editable":true,"mainField":"name"},"list":{"label":"role","searchable":true,"sortable":true}},"createdAt":{"edit":{"label":"createdAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"createdAt","searchable":true,"sortable":true}},"updatedAt":{"edit":{"label":"updatedAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"updatedAt","searchable":true,"sortable":true}},"createdBy":{"edit":{"label":"createdBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"createdBy","searchable":true,"sortable":true}},"updatedBy":{"edit":{"label":"updatedBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"updatedBy","searchable":true,"sortable":true}},"documentId":{"edit":{},"list":{"label":"documentId","searchable":true,"sortable":true}}},"layouts":{"list":["id","action","subject","role"],"edit":[[{"name":"action","size":6}],[{"name":"actionParameters","size":12}],[{"name":"subject","size":6}],[{"name":"properties","size":12}],[{"name":"conditions","size":12}],[{"name":"role","size":6}]]},"uid":"admin::permission"}	object	\N	\N
14	plugin_content_manager_configuration_content_types::admin::role	{"settings":{"bulkable":true,"filterable":true,"searchable":true,"pageSize":10,"mainField":"name","defaultSortBy":"name","defaultSortOrder":"ASC"},"metadatas":{"id":{"edit":{},"list":{"label":"id","searchable":true,"sortable":true}},"name":{"edit":{"label":"name","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"name","searchable":true,"sortable":true}},"code":{"edit":{"label":"code","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"code","searchable":true,"sortable":true}},"description":{"edit":{"label":"description","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"description","searchable":true,"sortable":true}},"users":{"edit":{"label":"users","description":"","placeholder":"","visible":true,"editable":true,"mainField":"firstname"},"list":{"label":"users","searchable":false,"sortable":false}},"permissions":{"edit":{"label":"permissions","description":"","placeholder":"","visible":true,"editable":true,"mainField":"action"},"list":{"label":"permissions","searchable":false,"sortable":false}},"createdAt":{"edit":{"label":"createdAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"createdAt","searchable":true,"sortable":true}},"updatedAt":{"edit":{"label":"updatedAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"updatedAt","searchable":true,"sortable":true}},"createdBy":{"edit":{"label":"createdBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"createdBy","searchable":true,"sortable":true}},"updatedBy":{"edit":{"label":"updatedBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"updatedBy","searchable":true,"sortable":true}},"documentId":{"edit":{},"list":{"label":"documentId","searchable":true,"sortable":true}}},"layouts":{"list":["id","name","code","description"],"edit":[[{"name":"name","size":6},{"name":"code","size":6}],[{"name":"description","size":6},{"name":"users","size":6}],[{"name":"permissions","size":6}]]},"uid":"admin::role"}	object	\N	\N
13	plugin_content_manager_configuration_content_types::admin::api-token-permission	{"settings":{"bulkable":true,"filterable":true,"searchable":true,"pageSize":10,"mainField":"action","defaultSortBy":"action","defaultSortOrder":"ASC"},"metadatas":{"id":{"edit":{},"list":{"label":"id","searchable":true,"sortable":true}},"action":{"edit":{"label":"action","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"action","searchable":true,"sortable":true}},"token":{"edit":{"label":"token","description":"","placeholder":"","visible":true,"editable":true,"mainField":"name"},"list":{"label":"token","searchable":true,"sortable":true}},"createdAt":{"edit":{"label":"createdAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"createdAt","searchable":true,"sortable":true}},"updatedAt":{"edit":{"label":"updatedAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"updatedAt","searchable":true,"sortable":true}},"createdBy":{"edit":{"label":"createdBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"createdBy","searchable":true,"sortable":true}},"updatedBy":{"edit":{"label":"updatedBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"updatedBy","searchable":true,"sortable":true}},"documentId":{"edit":{},"list":{"label":"documentId","searchable":true,"sortable":true}}},"layouts":{"list":["id","action","token","createdAt"],"edit":[[{"name":"action","size":6},{"name":"token","size":6}]]},"uid":"admin::api-token-permission"}	object	\N	\N
17	plugin_content_manager_configuration_content_types::admin::transfer-token	{"settings":{"bulkable":true,"filterable":true,"searchable":true,"pageSize":10,"mainField":"name","defaultSortBy":"name","defaultSortOrder":"ASC"},"metadatas":{"id":{"edit":{},"list":{"label":"id","searchable":true,"sortable":true}},"name":{"edit":{"label":"name","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"name","searchable":true,"sortable":true}},"description":{"edit":{"label":"description","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"description","searchable":true,"sortable":true}},"accessKey":{"edit":{"label":"accessKey","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"accessKey","searchable":true,"sortable":true}},"lastUsedAt":{"edit":{"label":"lastUsedAt","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"lastUsedAt","searchable":true,"sortable":true}},"permissions":{"edit":{"label":"permissions","description":"","placeholder":"","visible":true,"editable":true,"mainField":"action"},"list":{"label":"permissions","searchable":false,"sortable":false}},"expiresAt":{"edit":{"label":"expiresAt","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"expiresAt","searchable":true,"sortable":true}},"lifespan":{"edit":{"label":"lifespan","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"lifespan","searchable":true,"sortable":true}},"createdAt":{"edit":{"label":"createdAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"createdAt","searchable":true,"sortable":true}},"updatedAt":{"edit":{"label":"updatedAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"updatedAt","searchable":true,"sortable":true}},"createdBy":{"edit":{"label":"createdBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"createdBy","searchable":true,"sortable":true}},"updatedBy":{"edit":{"label":"updatedBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"updatedBy","searchable":true,"sortable":true}},"documentId":{"edit":{},"list":{"label":"documentId","searchable":true,"sortable":true}}},"layouts":{"list":["id","name","description","accessKey"],"edit":[[{"name":"name","size":6},{"name":"description","size":6}],[{"name":"accessKey","size":6},{"name":"lastUsedAt","size":6}],[{"name":"permissions","size":6},{"name":"expiresAt","size":6}],[{"name":"lifespan","size":4}]]},"uid":"admin::transfer-token"}	object	\N	\N
18	plugin_content_manager_configuration_content_types::admin::transfer-token-permission	{"settings":{"bulkable":true,"filterable":true,"searchable":true,"pageSize":10,"mainField":"action","defaultSortBy":"action","defaultSortOrder":"ASC"},"metadatas":{"id":{"edit":{},"list":{"label":"id","searchable":true,"sortable":true}},"action":{"edit":{"label":"action","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"action","searchable":true,"sortable":true}},"token":{"edit":{"label":"token","description":"","placeholder":"","visible":true,"editable":true,"mainField":"name"},"list":{"label":"token","searchable":true,"sortable":true}},"createdAt":{"edit":{"label":"createdAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"createdAt","searchable":true,"sortable":true}},"updatedAt":{"edit":{"label":"updatedAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"updatedAt","searchable":true,"sortable":true}},"createdBy":{"edit":{"label":"createdBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"createdBy","searchable":true,"sortable":true}},"updatedBy":{"edit":{"label":"updatedBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"updatedBy","searchable":true,"sortable":true}},"documentId":{"edit":{},"list":{"label":"documentId","searchable":true,"sortable":true}}},"layouts":{"list":["id","action","token","createdAt"],"edit":[[{"name":"action","size":6},{"name":"token","size":6}]]},"uid":"admin::transfer-token-permission"}	object	\N	\N
16	plugin_content_manager_configuration_content_types::admin::api-token	{"settings":{"bulkable":true,"filterable":true,"searchable":true,"pageSize":10,"mainField":"name","defaultSortBy":"name","defaultSortOrder":"ASC"},"metadatas":{"id":{"edit":{},"list":{"label":"id","searchable":true,"sortable":true}},"name":{"edit":{"label":"name","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"name","searchable":true,"sortable":true}},"description":{"edit":{"label":"description","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"description","searchable":true,"sortable":true}},"type":{"edit":{"label":"type","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"type","searchable":true,"sortable":true}},"accessKey":{"edit":{"label":"accessKey","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"accessKey","searchable":true,"sortable":true}},"encryptedKey":{"edit":{"label":"encryptedKey","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"encryptedKey","searchable":true,"sortable":true}},"lastUsedAt":{"edit":{"label":"lastUsedAt","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"lastUsedAt","searchable":true,"sortable":true}},"permissions":{"edit":{"label":"permissions","description":"","placeholder":"","visible":true,"editable":true,"mainField":"action"},"list":{"label":"permissions","searchable":false,"sortable":false}},"expiresAt":{"edit":{"label":"expiresAt","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"expiresAt","searchable":true,"sortable":true}},"lifespan":{"edit":{"label":"lifespan","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"lifespan","searchable":true,"sortable":true}},"createdAt":{"edit":{"label":"createdAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"createdAt","searchable":true,"sortable":true}},"updatedAt":{"edit":{"label":"updatedAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"updatedAt","searchable":true,"sortable":true}},"createdBy":{"edit":{"label":"createdBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"createdBy","searchable":true,"sortable":true}},"updatedBy":{"edit":{"label":"updatedBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"updatedBy","searchable":true,"sortable":true}},"documentId":{"edit":{},"list":{"label":"documentId","searchable":true,"sortable":true}}},"layouts":{"list":["id","name","description","type"],"edit":[[{"name":"name","size":6},{"name":"description","size":6}],[{"name":"type","size":6},{"name":"accessKey","size":6}],[{"name":"encryptedKey","size":6},{"name":"lastUsedAt","size":6}],[{"name":"permissions","size":6},{"name":"expiresAt","size":6}],[{"name":"lifespan","size":4}]]},"uid":"admin::api-token"}	object	\N	\N
19	plugin_upload_settings	{"sizeOptimization":true,"responsiveDimensions":true,"autoOrientation":false}	object	\N	\N
20	plugin_upload_view_configuration	{"pageSize":10,"sort":"createdAt:DESC"}	object	\N	\N
22	plugin_i18n_default_locale	"en"	string	\N	\N
23	plugin_users-permissions_grant	{"email":{"icon":"envelope","enabled":true},"discord":{"icon":"discord","enabled":false,"key":"","secret":"","callbackUrl":"api/auth/discord/callback","scope":["identify","email"]},"facebook":{"icon":"facebook-square","enabled":false,"key":"","secret":"","callbackUrl":"api/auth/facebook/callback","scope":["email"]},"google":{"icon":"google","enabled":false,"key":"","secret":"","callbackUrl":"api/auth/google/callback","scope":["email"]},"github":{"icon":"github","enabled":false,"key":"","secret":"","callbackUrl":"api/auth/github/callback","scope":["user","user:email"]},"microsoft":{"icon":"windows","enabled":false,"key":"","secret":"","callbackUrl":"api/auth/microsoft/callback","scope":["user.read"]},"twitter":{"icon":"twitter","enabled":false,"key":"","secret":"","callbackUrl":"api/auth/twitter/callback"},"instagram":{"icon":"instagram","enabled":false,"key":"","secret":"","callbackUrl":"api/auth/instagram/callback","scope":["user_profile"]},"vk":{"icon":"vk","enabled":false,"key":"","secret":"","callbackUrl":"api/auth/vk/callback","scope":["email"]},"twitch":{"icon":"twitch","enabled":false,"key":"","secret":"","callbackUrl":"api/auth/twitch/callback","scope":["user:read:email"]},"linkedin":{"icon":"linkedin","enabled":false,"key":"","secret":"","callbackUrl":"api/auth/linkedin/callback","scope":["r_liteprofile","r_emailaddress"]},"cognito":{"icon":"aws","enabled":false,"key":"","secret":"","subdomain":"my.subdomain.com","callback":"api/auth/cognito/callback","scope":["email","openid","profile"]},"reddit":{"icon":"reddit","enabled":false,"key":"","secret":"","callback":"api/auth/reddit/callback","scope":["identity"]},"auth0":{"icon":"","enabled":false,"key":"","secret":"","subdomain":"my-tenant.eu","callback":"api/auth/auth0/callback","scope":["openid","email","profile"]},"cas":{"icon":"book","enabled":false,"key":"","secret":"","callback":"api/auth/cas/callback","scope":["openid email"],"subdomain":"my.subdomain.com/cas"},"patreon":{"icon":"","enabled":false,"key":"","secret":"","callback":"api/auth/patreon/callback","scope":["identity","identity[email]"]},"keycloak":{"icon":"","enabled":false,"key":"","secret":"","subdomain":"myKeycloakProvider.com/realms/myrealm","callback":"api/auth/keycloak/callback","scope":["openid","email","profile"]}}	object	\N	\N
21	plugin_upload_metrics	{"weeklySchedule":"40 48 15 * * 0","lastWeeklyUpdate":1753602520048}	object	\N	\N
24	plugin_users-permissions_email	{"reset_password":{"display":"Email.template.reset_password","icon":"sync","options":{"from":{"name":"Administration Panel","email":"<EMAIL>"},"response_email":"","object":"Reset password","message":"<p>We heard that you lost your password. Sorry about that!</p>\\n\\n<p>But don’t worry! You can use the following link to reset your password:</p>\\n<p><%= URL %>?code=<%= TOKEN %></p>\\n\\n<p>Thanks.</p>"}},"email_confirmation":{"display":"Email.template.email_confirmation","icon":"check-square","options":{"from":{"name":"Administration Panel","email":"<EMAIL>"},"response_email":"","object":"Account confirmation","message":"<p>Thank you for registering!</p>\\n\\n<p>You have to confirm your email address. Please click on the link below.</p>\\n\\n<p><%= URL %>?confirmation=<%= CODE %></p>\\n\\n<p>Thanks.</p>"}}}	object	\N	\N
25	plugin_users-permissions_advanced	{"unique_email":true,"allow_register":true,"email_confirmation":false,"email_reset_password":null,"email_confirmation_redirection":null,"default_role":"authenticated"}	object	\N	\N
27	plugin_content_manager_configuration_content_types::api::team-member.team-member	{"settings":{"bulkable":true,"filterable":true,"searchable":true,"pageSize":10,"mainField":"name","defaultSortBy":"name","defaultSortOrder":"ASC"},"metadatas":{"id":{"edit":{},"list":{"label":"id","searchable":true,"sortable":true}},"name":{"edit":{"label":"name","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"name","searchable":true,"sortable":true}},"title":{"edit":{"label":"title","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"title","searchable":true,"sortable":true}},"role":{"edit":{"label":"role","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"role","searchable":true,"sortable":true}},"avatar":{"edit":{"label":"avatar","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"avatar","searchable":false,"sortable":false}},"email":{"edit":{"label":"email","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"email","searchable":true,"sortable":true}},"phone":{"edit":{"label":"phone","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"phone","searchable":true,"sortable":true}},"website":{"edit":{"label":"website","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"website","searchable":true,"sortable":true}},"education":{"edit":{"label":"education","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"education","searchable":false,"sortable":false}},"bio":{"edit":{"label":"bio","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"bio","searchable":false,"sortable":false}},"enrollmentYear":{"edit":{"label":"enrollmentYear","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"enrollmentYear","searchable":true,"sortable":true}},"graduationYear":{"edit":{"label":"graduationYear","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"graduationYear","searchable":true,"sortable":true}},"company":{"edit":{"label":"company","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"company","searchable":true,"sortable":true}},"position":{"edit":{"label":"position","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"position","searchable":true,"sortable":true}},"sortOrder":{"edit":{"label":"sortOrder","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"sortOrder","searchable":true,"sortable":true}},"researchDirection":{"edit":{"label":"researchDirection","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"researchDirection","searchable":false,"sortable":false}},"createdAt":{"edit":{"label":"createdAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"createdAt","searchable":true,"sortable":true}},"updatedAt":{"edit":{"label":"updatedAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"updatedAt","searchable":true,"sortable":true}},"createdBy":{"edit":{"label":"createdBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"createdBy","searchable":true,"sortable":true}},"updatedBy":{"edit":{"label":"updatedBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"updatedBy","searchable":true,"sortable":true}},"documentId":{"edit":{},"list":{"label":"documentId","searchable":true,"sortable":true}}},"layouts":{"list":["id","name","title","role"],"edit":[[{"name":"name","size":6},{"name":"title","size":6}],[{"name":"role","size":6},{"name":"avatar","size":6}],[{"name":"email","size":6}],[{"name":"phone","size":6},{"name":"website","size":6}],[{"name":"bio","size":12}],[{"name":"enrollmentYear","size":4}],[{"name":"graduationYear","size":4},{"name":"company","size":6}],[{"name":"position","size":6},{"name":"sortOrder","size":4}],[{"name":"education","size":12}],[{"name":"researchDirection","size":12}]]},"uid":"api::team-member.team-member"}	object	\N	\N
26	core_admin_auth	{"providers":{"autoRegister":false,"defaultRole":null,"ssoLockedRoles":null}}	object	\N	\N
28	plugin_content_manager_configuration_content_types::api::doi-collection.doi-collection	{"settings":{"bulkable":true,"filterable":true,"searchable":true,"pageSize":10,"mainField":"doi","defaultSortBy":"doi","defaultSortOrder":"ASC"},"metadatas":{"id":{"edit":{},"list":{"label":"id","searchable":true,"sortable":true}},"doi":{"edit":{"label":"doi","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"doi","searchable":true,"sortable":true}},"createdAt":{"edit":{"label":"createdAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"createdAt","searchable":true,"sortable":true}},"updatedAt":{"edit":{"label":"updatedAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"updatedAt","searchable":true,"sortable":true}},"createdBy":{"edit":{"label":"createdBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"createdBy","searchable":true,"sortable":true}},"updatedBy":{"edit":{"label":"updatedBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"updatedBy","searchable":true,"sortable":true}},"documentId":{"edit":{},"list":{"label":"documentId","searchable":true,"sortable":true}}},"layouts":{"list":["id","doi","createdAt","updatedAt"],"edit":[[{"name":"doi","size":6}]]},"uid":"api::doi-collection.doi-collection"}	object	\N	\N
29	plugin_content_manager_configuration_content_types::api::paper-info.paper-info	{"settings":{"bulkable":true,"filterable":true,"searchable":true,"pageSize":10,"mainField":"doi","defaultSortBy":"doi","defaultSortOrder":"ASC"},"metadatas":{"id":{"edit":{},"list":{"label":"id","searchable":true,"sortable":true}},"doi":{"edit":{"label":"doi","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"doi","searchable":true,"sortable":true}},"title":{"edit":{"label":"title","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"title","searchable":true,"sortable":true}},"authors":{"edit":{"label":"authors","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"authors","searchable":true,"sortable":true}},"year":{"edit":{"label":"year","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"year","searchable":true,"sortable":true}},"journal":{"edit":{"label":"journal","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"journal","searchable":true,"sortable":true}},"abstract":{"edit":{"label":"abstract","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"abstract","searchable":true,"sortable":true}},"bibtex":{"edit":{"label":"bibtex","description":"","placeholder":"","visible":true,"editable":true},"list":{"label":"bibtex","searchable":true,"sortable":true}},"createdAt":{"edit":{"label":"createdAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"createdAt","searchable":true,"sortable":true}},"updatedAt":{"edit":{"label":"updatedAt","description":"","placeholder":"","visible":false,"editable":true},"list":{"label":"updatedAt","searchable":true,"sortable":true}},"createdBy":{"edit":{"label":"createdBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"createdBy","searchable":true,"sortable":true}},"updatedBy":{"edit":{"label":"updatedBy","description":"","placeholder":"","visible":false,"editable":true,"mainField":"firstname"},"list":{"label":"updatedBy","searchable":true,"sortable":true}},"documentId":{"edit":{},"list":{"label":"documentId","searchable":true,"sortable":true}}},"layouts":{"list":["id","doi","title","authors"],"edit":[[{"name":"doi","size":6},{"name":"title","size":6}],[{"name":"authors","size":6},{"name":"year","size":4}],[{"name":"journal","size":6},{"name":"abstract","size":6}],[{"name":"bibtex","size":6}]]},"uid":"api::paper-info.paper-info"}	object	\N	\N
\.


--
-- Data for Name: strapi_database_schema; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.strapi_database_schema (id, schema, "time", hash) FROM stdin;
12	{"tables":[{"name":"doi_collections","indexes":[{"name":"doi_collections_documents_idx","columns":["document_id","locale","published_at"]},{"name":"doi_collections_created_by_id_fk","columns":["created_by_id"]},{"name":"doi_collections_updated_by_id_fk","columns":["updated_by_id"]}],"foreignKeys":[{"name":"doi_collections_created_by_id_fk","columns":["created_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"},{"name":"doi_collections_updated_by_id_fk","columns":["updated_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"document_id","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"doi","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"updated_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"published_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"updated_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"locale","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false}]},{"name":"paper_infos","indexes":[{"name":"paper_infos_documents_idx","columns":["document_id","locale","published_at"]},{"name":"paper_infos_created_by_id_fk","columns":["created_by_id"]},{"name":"paper_infos_updated_by_id_fk","columns":["updated_by_id"]}],"foreignKeys":[{"name":"paper_infos_created_by_id_fk","columns":["created_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"},{"name":"paper_infos_updated_by_id_fk","columns":["updated_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"document_id","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"doi","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"title","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"authors","type":"text","args":["longtext"],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"year","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"journal","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"abstract","type":"text","args":["longtext"],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"bibtex","type":"text","args":["longtext"],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"updated_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"published_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"updated_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"locale","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false}]},{"name":"team_members","indexes":[{"name":"team_members_documents_idx","columns":["document_id","locale","published_at"]},{"name":"team_members_created_by_id_fk","columns":["created_by_id"]},{"name":"team_members_updated_by_id_fk","columns":["updated_by_id"]}],"foreignKeys":[{"name":"team_members_created_by_id_fk","columns":["created_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"},{"name":"team_members_updated_by_id_fk","columns":["updated_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"document_id","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"name","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"title","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"role","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"research_direction","type":"jsonb","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"email","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"phone","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"website","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"education","type":"jsonb","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"bio","type":"jsonb","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"enrollment_year","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"graduation_year","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"company","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"position","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"sort_order","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"updated_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"published_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"updated_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"locale","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false}]},{"name":"files","indexes":[{"name":"upload_files_folder_path_index","columns":["folder_path"],"type":null},{"name":"upload_files_created_at_index","columns":["created_at"],"type":null},{"name":"upload_files_updated_at_index","columns":["updated_at"],"type":null},{"name":"upload_files_name_index","columns":["name"],"type":null},{"name":"upload_files_size_index","columns":["size"],"type":null},{"name":"upload_files_ext_index","columns":["ext"],"type":null},{"name":"files_documents_idx","columns":["document_id","locale","published_at"]},{"name":"files_created_by_id_fk","columns":["created_by_id"]},{"name":"files_updated_by_id_fk","columns":["updated_by_id"]}],"foreignKeys":[{"name":"files_created_by_id_fk","columns":["created_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"},{"name":"files_updated_by_id_fk","columns":["updated_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"document_id","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"name","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"alternative_text","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"caption","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"width","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"height","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"formats","type":"jsonb","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"hash","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"ext","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"mime","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"size","type":"decimal","args":[10,2],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"url","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"preview_url","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"provider","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"provider_metadata","type":"jsonb","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"folder_path","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"updated_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"published_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"updated_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"locale","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false}]},{"name":"upload_folders","indexes":[{"name":"upload_folders_path_id_index","columns":["path_id"],"type":"unique"},{"name":"upload_folders_path_index","columns":["path"],"type":"unique"},{"name":"upload_folders_documents_idx","columns":["document_id","locale","published_at"]},{"name":"upload_folders_created_by_id_fk","columns":["created_by_id"]},{"name":"upload_folders_updated_by_id_fk","columns":["updated_by_id"]}],"foreignKeys":[{"name":"upload_folders_created_by_id_fk","columns":["created_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"},{"name":"upload_folders_updated_by_id_fk","columns":["updated_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"document_id","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"name","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"path_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"path","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"updated_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"published_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"updated_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"locale","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false}]},{"name":"i18n_locale","indexes":[{"name":"i18n_locale_documents_idx","columns":["document_id","locale","published_at"]},{"name":"i18n_locale_created_by_id_fk","columns":["created_by_id"]},{"name":"i18n_locale_updated_by_id_fk","columns":["updated_by_id"]}],"foreignKeys":[{"name":"i18n_locale_created_by_id_fk","columns":["created_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"},{"name":"i18n_locale_updated_by_id_fk","columns":["updated_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"document_id","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"name","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"code","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"updated_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"published_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"updated_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"locale","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false}]},{"name":"strapi_releases","indexes":[{"name":"strapi_releases_documents_idx","columns":["document_id","locale","published_at"]},{"name":"strapi_releases_created_by_id_fk","columns":["created_by_id"]},{"name":"strapi_releases_updated_by_id_fk","columns":["updated_by_id"]}],"foreignKeys":[{"name":"strapi_releases_created_by_id_fk","columns":["created_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"},{"name":"strapi_releases_updated_by_id_fk","columns":["updated_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"document_id","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"name","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"released_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"scheduled_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"timezone","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"status","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"updated_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"published_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"updated_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"locale","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false}]},{"name":"strapi_release_actions","indexes":[{"name":"strapi_release_actions_documents_idx","columns":["document_id","locale","published_at"]},{"name":"strapi_release_actions_created_by_id_fk","columns":["created_by_id"]},{"name":"strapi_release_actions_updated_by_id_fk","columns":["updated_by_id"]}],"foreignKeys":[{"name":"strapi_release_actions_created_by_id_fk","columns":["created_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"},{"name":"strapi_release_actions_updated_by_id_fk","columns":["updated_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"document_id","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"type","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"content_type","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"entry_document_id","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"locale","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"is_entry_valid","type":"boolean","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"updated_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"published_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"updated_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true}]},{"name":"strapi_workflows","indexes":[{"name":"strapi_workflows_documents_idx","columns":["document_id","locale","published_at"]},{"name":"strapi_workflows_created_by_id_fk","columns":["created_by_id"]},{"name":"strapi_workflows_updated_by_id_fk","columns":["updated_by_id"]}],"foreignKeys":[{"name":"strapi_workflows_created_by_id_fk","columns":["created_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"},{"name":"strapi_workflows_updated_by_id_fk","columns":["updated_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"document_id","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"name","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"content_types","type":"jsonb","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"updated_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"published_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"updated_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"locale","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false}]},{"name":"strapi_workflows_stages","indexes":[{"name":"strapi_workflows_stages_documents_idx","columns":["document_id","locale","published_at"]},{"name":"strapi_workflows_stages_created_by_id_fk","columns":["created_by_id"]},{"name":"strapi_workflows_stages_updated_by_id_fk","columns":["updated_by_id"]}],"foreignKeys":[{"name":"strapi_workflows_stages_created_by_id_fk","columns":["created_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"},{"name":"strapi_workflows_stages_updated_by_id_fk","columns":["updated_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"document_id","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"name","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"color","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"updated_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"published_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"updated_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"locale","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false}]},{"name":"up_permissions","indexes":[{"name":"up_permissions_documents_idx","columns":["document_id","locale","published_at"]},{"name":"up_permissions_created_by_id_fk","columns":["created_by_id"]},{"name":"up_permissions_updated_by_id_fk","columns":["updated_by_id"]}],"foreignKeys":[{"name":"up_permissions_created_by_id_fk","columns":["created_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"},{"name":"up_permissions_updated_by_id_fk","columns":["updated_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"document_id","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"action","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"updated_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"published_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"updated_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"locale","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false}]},{"name":"up_roles","indexes":[{"name":"up_roles_documents_idx","columns":["document_id","locale","published_at"]},{"name":"up_roles_created_by_id_fk","columns":["created_by_id"]},{"name":"up_roles_updated_by_id_fk","columns":["updated_by_id"]}],"foreignKeys":[{"name":"up_roles_created_by_id_fk","columns":["created_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"},{"name":"up_roles_updated_by_id_fk","columns":["updated_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"document_id","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"name","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"description","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"type","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"updated_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"published_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"updated_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"locale","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false}]},{"name":"up_users","indexes":[{"name":"up_users_documents_idx","columns":["document_id","locale","published_at"]},{"name":"up_users_created_by_id_fk","columns":["created_by_id"]},{"name":"up_users_updated_by_id_fk","columns":["updated_by_id"]}],"foreignKeys":[{"name":"up_users_created_by_id_fk","columns":["created_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"},{"name":"up_users_updated_by_id_fk","columns":["updated_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"document_id","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"username","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"email","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"provider","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"password","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"reset_password_token","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"confirmation_token","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"confirmed","type":"boolean","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"blocked","type":"boolean","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"updated_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"published_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"updated_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"locale","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false}]},{"name":"admin_permissions","indexes":[{"name":"admin_permissions_documents_idx","columns":["document_id","locale","published_at"]},{"name":"admin_permissions_created_by_id_fk","columns":["created_by_id"]},{"name":"admin_permissions_updated_by_id_fk","columns":["updated_by_id"]}],"foreignKeys":[{"name":"admin_permissions_created_by_id_fk","columns":["created_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"},{"name":"admin_permissions_updated_by_id_fk","columns":["updated_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"document_id","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"action","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"action_parameters","type":"jsonb","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"subject","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"properties","type":"jsonb","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"conditions","type":"jsonb","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"updated_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"published_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"updated_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"locale","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false}]},{"name":"admin_users","indexes":[{"name":"admin_users_documents_idx","columns":["document_id","locale","published_at"]},{"name":"admin_users_created_by_id_fk","columns":["created_by_id"]},{"name":"admin_users_updated_by_id_fk","columns":["updated_by_id"]}],"foreignKeys":[{"name":"admin_users_created_by_id_fk","columns":["created_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"},{"name":"admin_users_updated_by_id_fk","columns":["updated_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"document_id","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"firstname","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"lastname","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"username","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"email","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"password","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"reset_password_token","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"registration_token","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"is_active","type":"boolean","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"blocked","type":"boolean","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"prefered_language","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"updated_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"published_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"updated_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"locale","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false}]},{"name":"admin_roles","indexes":[{"name":"admin_roles_documents_idx","columns":["document_id","locale","published_at"]},{"name":"admin_roles_created_by_id_fk","columns":["created_by_id"]},{"name":"admin_roles_updated_by_id_fk","columns":["updated_by_id"]}],"foreignKeys":[{"name":"admin_roles_created_by_id_fk","columns":["created_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"},{"name":"admin_roles_updated_by_id_fk","columns":["updated_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"document_id","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"name","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"code","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"description","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"updated_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"published_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"updated_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"locale","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false}]},{"name":"strapi_api_tokens","indexes":[{"name":"strapi_api_tokens_documents_idx","columns":["document_id","locale","published_at"]},{"name":"strapi_api_tokens_created_by_id_fk","columns":["created_by_id"]},{"name":"strapi_api_tokens_updated_by_id_fk","columns":["updated_by_id"]}],"foreignKeys":[{"name":"strapi_api_tokens_created_by_id_fk","columns":["created_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"},{"name":"strapi_api_tokens_updated_by_id_fk","columns":["updated_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"document_id","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"name","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"description","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"type","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"access_key","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"encrypted_key","type":"text","args":["longtext"],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"last_used_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"expires_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"lifespan","type":"bigInteger","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"updated_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"published_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"updated_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"locale","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false}]},{"name":"strapi_api_token_permissions","indexes":[{"name":"strapi_api_token_permissions_documents_idx","columns":["document_id","locale","published_at"]},{"name":"strapi_api_token_permissions_created_by_id_fk","columns":["created_by_id"]},{"name":"strapi_api_token_permissions_updated_by_id_fk","columns":["updated_by_id"]}],"foreignKeys":[{"name":"strapi_api_token_permissions_created_by_id_fk","columns":["created_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"},{"name":"strapi_api_token_permissions_updated_by_id_fk","columns":["updated_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"document_id","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"action","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"updated_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"published_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"updated_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"locale","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false}]},{"name":"strapi_transfer_tokens","indexes":[{"name":"strapi_transfer_tokens_documents_idx","columns":["document_id","locale","published_at"]},{"name":"strapi_transfer_tokens_created_by_id_fk","columns":["created_by_id"]},{"name":"strapi_transfer_tokens_updated_by_id_fk","columns":["updated_by_id"]}],"foreignKeys":[{"name":"strapi_transfer_tokens_created_by_id_fk","columns":["created_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"},{"name":"strapi_transfer_tokens_updated_by_id_fk","columns":["updated_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"document_id","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"name","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"description","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"access_key","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"last_used_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"expires_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"lifespan","type":"bigInteger","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"updated_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"published_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"updated_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"locale","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false}]},{"name":"strapi_transfer_token_permissions","indexes":[{"name":"strapi_transfer_token_permissions_documents_idx","columns":["document_id","locale","published_at"]},{"name":"strapi_transfer_token_permissions_created_by_id_fk","columns":["created_by_id"]},{"name":"strapi_transfer_token_permissions_updated_by_id_fk","columns":["updated_by_id"]}],"foreignKeys":[{"name":"strapi_transfer_token_permissions_created_by_id_fk","columns":["created_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"},{"name":"strapi_transfer_token_permissions_updated_by_id_fk","columns":["updated_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"document_id","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"action","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"updated_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"published_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"updated_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"locale","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false}]},{"name":"strapi_core_store_settings","indexes":[],"foreignKeys":[],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"key","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"value","type":"text","args":["longtext"],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"type","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"environment","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"tag","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false}]},{"name":"strapi_webhooks","indexes":[],"foreignKeys":[],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"name","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"url","type":"text","args":["longtext"],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"headers","type":"jsonb","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"events","type":"jsonb","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"enabled","type":"boolean","args":[],"defaultTo":null,"notNullable":false,"unsigned":false}]},{"name":"strapi_history_versions","indexes":[{"name":"strapi_history_versions_created_by_id_fk","columns":["created_by_id"]}],"foreignKeys":[{"name":"strapi_history_versions_created_by_id_fk","columns":["created_by_id"],"referencedTable":"admin_users","referencedColumns":["id"],"onDelete":"SET NULL"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"content_type","type":"string","args":[],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"related_document_id","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"locale","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"status","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"data","type":"jsonb","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"schema","type":"jsonb","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_at","type":"datetime","args":[{"useTz":false,"precision":6}],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"created_by_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true}]},{"name":"files_related_mph","indexes":[{"name":"files_related_mph_fk","columns":["file_id"]},{"name":"files_related_mph_oidx","columns":["order"]},{"name":"files_related_mph_idix","columns":["related_id"]}],"foreignKeys":[{"name":"files_related_mph_fk","columns":["file_id"],"referencedColumns":["id"],"referencedTable":"files","onDelete":"CASCADE"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"file_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"related_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"related_type","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"field","type":"string","args":[],"defaultTo":null,"notNullable":false,"unsigned":false},{"name":"order","type":"double","args":[],"defaultTo":null,"notNullable":false,"unsigned":true}]},{"name":"files_folder_lnk","indexes":[{"name":"files_folder_lnk_fk","columns":["file_id"]},{"name":"files_folder_lnk_ifk","columns":["folder_id"]},{"name":"files_folder_lnk_uq","columns":["file_id","folder_id"],"type":"unique"},{"name":"files_folder_lnk_oifk","columns":["file_ord"]}],"foreignKeys":[{"name":"files_folder_lnk_fk","columns":["file_id"],"referencedColumns":["id"],"referencedTable":"files","onDelete":"CASCADE"},{"name":"files_folder_lnk_ifk","columns":["folder_id"],"referencedColumns":["id"],"referencedTable":"upload_folders","onDelete":"CASCADE"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"file_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"folder_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"file_ord","type":"double","args":[],"defaultTo":null,"notNullable":false,"unsigned":true}]},{"name":"upload_folders_parent_lnk","indexes":[{"name":"upload_folders_parent_lnk_fk","columns":["folder_id"]},{"name":"upload_folders_parent_lnk_ifk","columns":["inv_folder_id"]},{"name":"upload_folders_parent_lnk_uq","columns":["folder_id","inv_folder_id"],"type":"unique"},{"name":"upload_folders_parent_lnk_oifk","columns":["folder_ord"]}],"foreignKeys":[{"name":"upload_folders_parent_lnk_fk","columns":["folder_id"],"referencedColumns":["id"],"referencedTable":"upload_folders","onDelete":"CASCADE"},{"name":"upload_folders_parent_lnk_ifk","columns":["inv_folder_id"],"referencedColumns":["id"],"referencedTable":"upload_folders","onDelete":"CASCADE"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"folder_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"inv_folder_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"folder_ord","type":"double","args":[],"defaultTo":null,"notNullable":false,"unsigned":true}]},{"name":"strapi_release_actions_release_lnk","indexes":[{"name":"strapi_release_actions_release_lnk_fk","columns":["release_action_id"]},{"name":"strapi_release_actions_release_lnk_ifk","columns":["release_id"]},{"name":"strapi_release_actions_release_lnk_uq","columns":["release_action_id","release_id"],"type":"unique"},{"name":"strapi_release_actions_release_lnk_oifk","columns":["release_action_ord"]}],"foreignKeys":[{"name":"strapi_release_actions_release_lnk_fk","columns":["release_action_id"],"referencedColumns":["id"],"referencedTable":"strapi_release_actions","onDelete":"CASCADE"},{"name":"strapi_release_actions_release_lnk_ifk","columns":["release_id"],"referencedColumns":["id"],"referencedTable":"strapi_releases","onDelete":"CASCADE"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"release_action_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"release_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"release_action_ord","type":"double","args":[],"defaultTo":null,"notNullable":false,"unsigned":true}]},{"name":"strapi_workflows_stage_required_to_publish_lnk","indexes":[{"name":"strapi_workflows_stage_required_to_publish_lnk_fk","columns":["workflow_id"]},{"name":"strapi_workflows_stage_required_to_publish_lnk_ifk","columns":["workflow_stage_id"]},{"name":"strapi_workflows_stage_required_to_publish_lnk_uq","columns":["workflow_id","workflow_stage_id"],"type":"unique"}],"foreignKeys":[{"name":"strapi_workflows_stage_required_to_publish_lnk_fk","columns":["workflow_id"],"referencedColumns":["id"],"referencedTable":"strapi_workflows","onDelete":"CASCADE"},{"name":"strapi_workflows_stage_required_to_publish_lnk_ifk","columns":["workflow_stage_id"],"referencedColumns":["id"],"referencedTable":"strapi_workflows_stages","onDelete":"CASCADE"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"workflow_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"workflow_stage_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true}]},{"name":"strapi_workflows_stages_workflow_lnk","indexes":[{"name":"strapi_workflows_stages_workflow_lnk_fk","columns":["workflow_stage_id"]},{"name":"strapi_workflows_stages_workflow_lnk_ifk","columns":["workflow_id"]},{"name":"strapi_workflows_stages_workflow_lnk_uq","columns":["workflow_stage_id","workflow_id"],"type":"unique"},{"name":"strapi_workflows_stages_workflow_lnk_oifk","columns":["workflow_stage_ord"]}],"foreignKeys":[{"name":"strapi_workflows_stages_workflow_lnk_fk","columns":["workflow_stage_id"],"referencedColumns":["id"],"referencedTable":"strapi_workflows_stages","onDelete":"CASCADE"},{"name":"strapi_workflows_stages_workflow_lnk_ifk","columns":["workflow_id"],"referencedColumns":["id"],"referencedTable":"strapi_workflows","onDelete":"CASCADE"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"workflow_stage_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"workflow_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"workflow_stage_ord","type":"double","args":[],"defaultTo":null,"notNullable":false,"unsigned":true}]},{"name":"strapi_workflows_stages_permissions_lnk","indexes":[{"name":"strapi_workflows_stages_permissions_lnk_fk","columns":["workflow_stage_id"]},{"name":"strapi_workflows_stages_permissions_lnk_ifk","columns":["permission_id"]},{"name":"strapi_workflows_stages_permissions_lnk_uq","columns":["workflow_stage_id","permission_id"],"type":"unique"},{"name":"strapi_workflows_stages_permissions_lnk_ofk","columns":["permission_ord"]}],"foreignKeys":[{"name":"strapi_workflows_stages_permissions_lnk_fk","columns":["workflow_stage_id"],"referencedColumns":["id"],"referencedTable":"strapi_workflows_stages","onDelete":"CASCADE"},{"name":"strapi_workflows_stages_permissions_lnk_ifk","columns":["permission_id"],"referencedColumns":["id"],"referencedTable":"admin_permissions","onDelete":"CASCADE"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"workflow_stage_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"permission_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"permission_ord","type":"double","args":[],"defaultTo":null,"notNullable":false,"unsigned":true}]},{"name":"up_permissions_role_lnk","indexes":[{"name":"up_permissions_role_lnk_fk","columns":["permission_id"]},{"name":"up_permissions_role_lnk_ifk","columns":["role_id"]},{"name":"up_permissions_role_lnk_uq","columns":["permission_id","role_id"],"type":"unique"},{"name":"up_permissions_role_lnk_oifk","columns":["permission_ord"]}],"foreignKeys":[{"name":"up_permissions_role_lnk_fk","columns":["permission_id"],"referencedColumns":["id"],"referencedTable":"up_permissions","onDelete":"CASCADE"},{"name":"up_permissions_role_lnk_ifk","columns":["role_id"],"referencedColumns":["id"],"referencedTable":"up_roles","onDelete":"CASCADE"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"permission_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"role_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"permission_ord","type":"double","args":[],"defaultTo":null,"notNullable":false,"unsigned":true}]},{"name":"up_users_role_lnk","indexes":[{"name":"up_users_role_lnk_fk","columns":["user_id"]},{"name":"up_users_role_lnk_ifk","columns":["role_id"]},{"name":"up_users_role_lnk_uq","columns":["user_id","role_id"],"type":"unique"},{"name":"up_users_role_lnk_oifk","columns":["user_ord"]}],"foreignKeys":[{"name":"up_users_role_lnk_fk","columns":["user_id"],"referencedColumns":["id"],"referencedTable":"up_users","onDelete":"CASCADE"},{"name":"up_users_role_lnk_ifk","columns":["role_id"],"referencedColumns":["id"],"referencedTable":"up_roles","onDelete":"CASCADE"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"user_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"role_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"user_ord","type":"double","args":[],"defaultTo":null,"notNullable":false,"unsigned":true}]},{"name":"admin_permissions_role_lnk","indexes":[{"name":"admin_permissions_role_lnk_fk","columns":["permission_id"]},{"name":"admin_permissions_role_lnk_ifk","columns":["role_id"]},{"name":"admin_permissions_role_lnk_uq","columns":["permission_id","role_id"],"type":"unique"},{"name":"admin_permissions_role_lnk_oifk","columns":["permission_ord"]}],"foreignKeys":[{"name":"admin_permissions_role_lnk_fk","columns":["permission_id"],"referencedColumns":["id"],"referencedTable":"admin_permissions","onDelete":"CASCADE"},{"name":"admin_permissions_role_lnk_ifk","columns":["role_id"],"referencedColumns":["id"],"referencedTable":"admin_roles","onDelete":"CASCADE"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"permission_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"role_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"permission_ord","type":"double","args":[],"defaultTo":null,"notNullable":false,"unsigned":true}]},{"name":"admin_users_roles_lnk","indexes":[{"name":"admin_users_roles_lnk_fk","columns":["user_id"]},{"name":"admin_users_roles_lnk_ifk","columns":["role_id"]},{"name":"admin_users_roles_lnk_uq","columns":["user_id","role_id"],"type":"unique"},{"name":"admin_users_roles_lnk_ofk","columns":["role_ord"]},{"name":"admin_users_roles_lnk_oifk","columns":["user_ord"]}],"foreignKeys":[{"name":"admin_users_roles_lnk_fk","columns":["user_id"],"referencedColumns":["id"],"referencedTable":"admin_users","onDelete":"CASCADE"},{"name":"admin_users_roles_lnk_ifk","columns":["role_id"],"referencedColumns":["id"],"referencedTable":"admin_roles","onDelete":"CASCADE"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"user_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"role_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"role_ord","type":"double","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"user_ord","type":"double","args":[],"defaultTo":null,"notNullable":false,"unsigned":true}]},{"name":"strapi_api_token_permissions_token_lnk","indexes":[{"name":"strapi_api_token_permissions_token_lnk_fk","columns":["api_token_permission_id"]},{"name":"strapi_api_token_permissions_token_lnk_ifk","columns":["api_token_id"]},{"name":"strapi_api_token_permissions_token_lnk_uq","columns":["api_token_permission_id","api_token_id"],"type":"unique"},{"name":"strapi_api_token_permissions_token_lnk_oifk","columns":["api_token_permission_ord"]}],"foreignKeys":[{"name":"strapi_api_token_permissions_token_lnk_fk","columns":["api_token_permission_id"],"referencedColumns":["id"],"referencedTable":"strapi_api_token_permissions","onDelete":"CASCADE"},{"name":"strapi_api_token_permissions_token_lnk_ifk","columns":["api_token_id"],"referencedColumns":["id"],"referencedTable":"strapi_api_tokens","onDelete":"CASCADE"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"api_token_permission_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"api_token_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"api_token_permission_ord","type":"double","args":[],"defaultTo":null,"notNullable":false,"unsigned":true}]},{"name":"strapi_transfer_token_permissions_token_lnk","indexes":[{"name":"strapi_transfer_token_permissions_token_lnk_fk","columns":["transfer_token_permission_id"]},{"name":"strapi_transfer_token_permissions_token_lnk_ifk","columns":["transfer_token_id"]},{"name":"strapi_transfer_token_permissions_token_lnk_uq","columns":["transfer_token_permission_id","transfer_token_id"],"type":"unique"},{"name":"strapi_transfer_token_permissions_token_lnk_oifk","columns":["transfer_token_permission_ord"]}],"foreignKeys":[{"name":"strapi_transfer_token_permissions_token_lnk_fk","columns":["transfer_token_permission_id"],"referencedColumns":["id"],"referencedTable":"strapi_transfer_token_permissions","onDelete":"CASCADE"},{"name":"strapi_transfer_token_permissions_token_lnk_ifk","columns":["transfer_token_id"],"referencedColumns":["id"],"referencedTable":"strapi_transfer_tokens","onDelete":"CASCADE"}],"columns":[{"name":"id","type":"increments","args":[{"primary":true,"primaryKey":true}],"defaultTo":null,"notNullable":true,"unsigned":false},{"name":"transfer_token_permission_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"transfer_token_id","type":"integer","args":[],"defaultTo":null,"notNullable":false,"unsigned":true},{"name":"transfer_token_permission_ord","type":"double","args":[],"defaultTo":null,"notNullable":false,"unsigned":true}]}]}	2025-07-29 22:54:22.313	f19ac8d9352ec5343274a2bfaf2fd99e121f5573b18c413f24130c4e0301b2b2
\.


--
-- Data for Name: strapi_history_versions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.strapi_history_versions (id, content_type, related_document_id, locale, status, data, schema, created_at, created_by_id) FROM stdin;
\.


--
-- Data for Name: strapi_migrations; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.strapi_migrations (id, name, "time") FROM stdin;
\.


--
-- Data for Name: strapi_migrations_internal; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.strapi_migrations_internal (id, name, "time") FROM stdin;
1	5.0.0-rename-identifiers-longer-than-max-length	2025-07-20 15:48:24.78
2	5.0.0-02-created-document-id	2025-07-20 15:48:24.843
3	5.0.0-03-created-locale	2025-07-20 15:48:24.905
4	5.0.0-04-created-published-at	2025-07-20 15:48:24.975
5	5.0.0-05-drop-slug-fields-index	2025-07-20 15:48:25.028
6	core::5.0.0-discard-drafts	2025-07-20 15:48:25.087
\.


--
-- Data for Name: strapi_release_actions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.strapi_release_actions (id, document_id, type, content_type, entry_document_id, locale, is_entry_valid, created_at, updated_at, published_at, created_by_id, updated_by_id) FROM stdin;
\.


--
-- Data for Name: strapi_release_actions_release_lnk; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.strapi_release_actions_release_lnk (id, release_action_id, release_id, release_action_ord) FROM stdin;
\.


--
-- Data for Name: strapi_releases; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.strapi_releases (id, document_id, name, released_at, scheduled_at, timezone, status, created_at, updated_at, published_at, created_by_id, updated_by_id, locale) FROM stdin;
\.


--
-- Data for Name: strapi_transfer_token_permissions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.strapi_transfer_token_permissions (id, document_id, action, created_at, updated_at, published_at, created_by_id, updated_by_id, locale) FROM stdin;
\.


--
-- Data for Name: strapi_transfer_token_permissions_token_lnk; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.strapi_transfer_token_permissions_token_lnk (id, transfer_token_permission_id, transfer_token_id, transfer_token_permission_ord) FROM stdin;
\.


--
-- Data for Name: strapi_transfer_tokens; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.strapi_transfer_tokens (id, document_id, name, description, access_key, last_used_at, expires_at, lifespan, created_at, updated_at, published_at, created_by_id, updated_by_id, locale) FROM stdin;
\.


--
-- Data for Name: strapi_webhooks; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.strapi_webhooks (id, name, url, headers, events, enabled) FROM stdin;
\.


--
-- Data for Name: strapi_workflows; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.strapi_workflows (id, document_id, name, content_types, created_at, updated_at, published_at, created_by_id, updated_by_id, locale) FROM stdin;
\.


--
-- Data for Name: strapi_workflows_stage_required_to_publish_lnk; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.strapi_workflows_stage_required_to_publish_lnk (id, workflow_id, workflow_stage_id) FROM stdin;
\.


--
-- Data for Name: strapi_workflows_stages; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.strapi_workflows_stages (id, document_id, name, color, created_at, updated_at, published_at, created_by_id, updated_by_id, locale) FROM stdin;
\.


--
-- Data for Name: strapi_workflows_stages_permissions_lnk; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.strapi_workflows_stages_permissions_lnk (id, workflow_stage_id, permission_id, permission_ord) FROM stdin;
\.


--
-- Data for Name: strapi_workflows_stages_workflow_lnk; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.strapi_workflows_stages_workflow_lnk (id, workflow_stage_id, workflow_id, workflow_stage_ord) FROM stdin;
\.


--
-- Data for Name: team_members; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.team_members (id, document_id, name, title, role, email, phone, website, bio, enrollment_year, graduation_year, company, "position", sort_order, created_at, updated_at, published_at, created_by_id, updated_by_id, locale, education, research_direction) FROM stdin;
11	kikg8c7x5p41g5fwffe7khvr	王隆	硕士在读	Master	<EMAIL>	\N	\N	\N	2024	\N	\N	\N	\N	2025-07-27 01:00:53.082	2025-07-27 14:15:23.241	\N	1	1	\N	\N	[{"type": "paragraph", "children": [{"text": "待定", "type": "text"}]}]
1	go4x4cze2hncuejy8qy4ng4h	李清泉	中国工程院院士，教授，博士生导师	Mentor	<EMAIL>	\N	https://geospatial.szu.edu.cn/info/1156/5901.htm	[{"type": "paragraph", "children": [{"text": "李清泉，男，汉族，1965年生，中国工程院院士，国际欧亚科学院院士，现任深圳大学党委书记。武汉测绘科技大学摄影测量与遥感专业毕业，工学博士，二级教授，博士生导师，973首席科学家，“十一五”科技部863计划现代交通领域专家组成员，第三批“广东特支计划”杰出人才。长期从事动态精密工程测量创新理论和自主装备研究，该领域是测绘、土木、信息等学科融合的国际学术前沿。针对变形测量面临的“测快、测全、测准”挑战，构建动态精密工程测量理论方法，在瞬时变形、表观变形和内部变形测量技术上取得开创性突破，发明研制公路、铁路、地铁、市政、水利等行业自主测量装备，研究成果在全国32个省市自治区的公路、铁路、地铁、水利等行业规模化应用，实现装备自主可控、国产替代和出口海外，引领学科创新发展，推动行业技术跨越，确立动态精密工程测量的国际领先地位。研究成果实现产业化，服务青藏铁路、冬奥会速滑馆、深中通道等国家重大工程。", "type": "text"}]}, {"type": "paragraph", "children": [{"text": "李清泉现任中国测绘学会副理事长、国际城市信息学会副主席、国际欧亚科学院院士、Urban Informatics期刊亚太地区主编。获国家技术发明二等奖1项，国家科技进步二等奖1项，国家科技进步奖（创新团队）1项，何梁何利科技进步奖，全国创新争先奖，中国青年科技奖，省部级/一级学会一等奖多项和国际移动测量杰出贡献奖等。授权发明专利80余项，其中国际发明专利10项，主编参编标准规范7部，出版本领域首部中英文专著《动态精密工程测量》等著作7部，发表SCI论文260余篇，谷歌引用超2万次，H指数76，入选斯坦福大学“终身科学影响力排行榜”。", "type": "text"}]}]	\N	\N	\N	\N	1	2025-07-26 23:21:01.77	2025-07-27 14:12:56.123	\N	1	1	\N	\N	[{"type": "list", "format": "unordered", "children": [{"type": "list-item", "children": [{"text": "动态精密工程测量创新理论和自主装备研究", "type": "text"}]}]}]
6	m2hu7982crlsipuucfbx5r5h	高琦丽	助理教授	Mentor	<EMAIL>	\N	https://www.gaoqili.cn	[{"type": "paragraph", "children": [{"text": "Dr. Qili Gao is an Assistant Professor at Shenzhen University. She received her Ph.D. degree from Wuhan University, China. Her research interests include spatial-temporal data mining, urban informatics, and human mobility and social inequality analysis for urban and transport planning. She has published several papers on human mobility patterns, jobs-housing relationships, socio-spatial segregation and transport-based inequality. She was also awarded the National Natural Science Foundation of China for Youth and the Postdoctoral Science Foundation of China.", "type": "text"}]}]	\N	\N	\N	\N	3	2025-07-27 00:49:12.809	2025-07-27 14:14:01.101	\N	1	1	\N	[{"type": "code", "children": [{"text": "2019    PhD in Geographic Information Science\\n        Wuhan University\\n2015    MEng in Survey and Mapping Engineering\\n        Wuhan University\\n2013    BSc in Remote Sensing Science and Technology\\n        Wuhan University", "type": "text"}], "language": "plaintext"}]	[{"type": "list", "format": "unordered", "children": [{"type": "list-item", "children": [{"text": "Human Dynamics and Urban Informatics", "type": "text"}]}]}, {"type": "list", "format": "unordered", "children": [{"type": "list-item", "children": [{"text": "Spatial Data Mining and Social Computing", "type": "text"}]}]}, {"type": "list", "format": "unordered", "children": [{"type": "list-item", "children": [{"text": "Human Mobility and Social Inequality", "type": "text"}]}]}, {"type": "list", "format": "unordered", "children": [{"type": "list-item", "children": [{"text": "Data-driven Urban Analytics", "type": "text"}]}]}, {"type": "list", "format": "unordered", "children": [{"type": "list-item", "children": [{"text": "Economic Geography and Fintech", "type": "text"}]}]}]
14	e61kok6wq4dqg3o05xue660j	王子阳	硕士在读	Master	<EMAIL>	\N	\N	\N	2024	\N	\N	\N	\N	2025-07-27 01:03:36.766	2025-07-27 14:15:14.248	\N	1	1	\N	\N	[{"type": "paragraph", "children": [{"text": "待定", "type": "text"}]}]
3	xu7f0fuyawsrwojsw7n8u47h	乐阳	教授，博士生导师	Mentor	<EMAIL>	\N	https://facultyprofiles.hkust-gz.edu.cn/faculty-personal-page?id=525	[{"type": "paragraph", "children": [{"text": "Dr. Yang Yue is a professor of urban informatics. Her research and teaching focus on innovations at the nexus of urban big data, GeoAI, and social sustainability.", "type": "text"}]}, {"type": "paragraph", "children": [{"text": "Dr. Yue got degrees from the University of Hong Kong and Wuhan University, respectively. Before joining the UGOD Thrust of HKUST (GZ), she had been a professor at Shenzhen University and associate professor at the State Key Laboratory of Information Engineering in Surveying, Mapping, and Remote Sensing, Wuhan University. She also serves as associate editor of Computers, Environment, and Urban Systems, vice chair of the ACM SIGSPATIAL China Chapter, and co-chair of the Special Committee on Social and Remote Sensing for Computational Social Science.", "type": "text"}]}, {"type": "paragraph", "children": [{"text": "Dr. Yue has secured over 20M in research and teaching grants and authored approximately 100 peer-reviewed articles, including highly cited works in both English and Chinese. Her education philosophy revolves around nurturing the next generation of geospatial professionals with a focus on smart and sustainable cities. She won teaching and course awards from the university, Guangdong province, and the National Ministry of Education.", "type": "text"}]}]	\N	\N	\N	\N	2	2025-07-27 00:43:59.468	2025-07-27 14:12:19.044	\N	1	1	\N	[{"type": "code", "children": [{"text": "2001-2006    Ph.D. in Intelligent Transportation\\n             University of Hong Kong, Hong Kong SAR\\n1996-1999    M.Sc. in Photogrammetry and Remote Sensing (GIS)\\n             Wuhan Technical University of Survey and Mapping (now Wuhan University), P. R. China\\n1992-1996    B.Sc. in Surveying and Geomatic Engineering\\n             Wuhan Technical University of Survey and Mapping (now Wuhan University), P. R. China", "type": "text"}], "language": "plaintext"}]	[{"type": "list", "format": "unordered", "children": [{"type": "list-item", "children": [{"text": "Urban big data", "type": "text"}]}, {"type": "list-item", "children": [{"text": "GeoAI", "type": "text"}]}, {"type": "list-item", "children": [{"text": "Social sustainability", "type": "text"}]}]}]
25	xu7f0fuyawsrwojsw7n8u47h	乐阳	教授，博士生导师	Mentor	<EMAIL>	\N	https://facultyprofiles.hkust-gz.edu.cn/faculty-personal-page?id=525	[{"type": "paragraph", "children": [{"text": "Dr. Yang Yue is a professor of urban informatics. Her research and teaching focus on innovations at the nexus of urban big data, GeoAI, and social sustainability.", "type": "text"}]}, {"type": "paragraph", "children": [{"text": "Dr. Yue got degrees from the University of Hong Kong and Wuhan University, respectively. Before joining the UGOD Thrust of HKUST (GZ), she had been a professor at Shenzhen University and associate professor at the State Key Laboratory of Information Engineering in Surveying, Mapping, and Remote Sensing, Wuhan University. She also serves as associate editor of Computers, Environment, and Urban Systems, vice chair of the ACM SIGSPATIAL China Chapter, and co-chair of the Special Committee on Social and Remote Sensing for Computational Social Science.", "type": "text"}]}, {"type": "paragraph", "children": [{"text": "Dr. Yue has secured over 20M in research and teaching grants and authored approximately 100 peer-reviewed articles, including highly cited works in both English and Chinese. Her education philosophy revolves around nurturing the next generation of geospatial professionals with a focus on smart and sustainable cities. She won teaching and course awards from the university, Guangdong province, and the National Ministry of Education.", "type": "text"}]}]	\N	\N	\N	\N	2	2025-07-27 00:43:59.468	2025-07-27 14:12:19.044	2025-07-27 14:12:19.067	1	1	\N	[{"type": "code", "children": [{"text": "2001-2006    Ph.D. in Intelligent Transportation\\n             University of Hong Kong, Hong Kong SAR\\n1996-1999    M.Sc. in Photogrammetry and Remote Sensing (GIS)\\n             Wuhan Technical University of Survey and Mapping (now Wuhan University), P. R. China\\n1992-1996    B.Sc. in Surveying and Geomatic Engineering\\n             Wuhan Technical University of Survey and Mapping (now Wuhan University), P. R. China", "type": "text"}], "language": "plaintext"}]	[{"type": "list", "format": "unordered", "children": [{"type": "list-item", "children": [{"text": "Urban big data", "type": "text"}]}, {"type": "list-item", "children": [{"text": "GeoAI", "type": "text"}]}, {"type": "list-item", "children": [{"text": "Social sustainability", "type": "text"}]}]}]
26	go4x4cze2hncuejy8qy4ng4h	李清泉	中国工程院院士，教授，博士生导师	Mentor	<EMAIL>	\N	https://geospatial.szu.edu.cn/info/1156/5901.htm	[{"type": "paragraph", "children": [{"text": "李清泉，男，汉族，1965年生，中国工程院院士，国际欧亚科学院院士，现任深圳大学党委书记。武汉测绘科技大学摄影测量与遥感专业毕业，工学博士，二级教授，博士生导师，973首席科学家，“十一五”科技部863计划现代交通领域专家组成员，第三批“广东特支计划”杰出人才。长期从事动态精密工程测量创新理论和自主装备研究，该领域是测绘、土木、信息等学科融合的国际学术前沿。针对变形测量面临的“测快、测全、测准”挑战，构建动态精密工程测量理论方法，在瞬时变形、表观变形和内部变形测量技术上取得开创性突破，发明研制公路、铁路、地铁、市政、水利等行业自主测量装备，研究成果在全国32个省市自治区的公路、铁路、地铁、水利等行业规模化应用，实现装备自主可控、国产替代和出口海外，引领学科创新发展，推动行业技术跨越，确立动态精密工程测量的国际领先地位。研究成果实现产业化，服务青藏铁路、冬奥会速滑馆、深中通道等国家重大工程。", "type": "text"}]}, {"type": "paragraph", "children": [{"text": "李清泉现任中国测绘学会副理事长、国际城市信息学会副主席、国际欧亚科学院院士、Urban Informatics期刊亚太地区主编。获国家技术发明二等奖1项，国家科技进步二等奖1项，国家科技进步奖（创新团队）1项，何梁何利科技进步奖，全国创新争先奖，中国青年科技奖，省部级/一级学会一等奖多项和国际移动测量杰出贡献奖等。授权发明专利80余项，其中国际发明专利10项，主编参编标准规范7部，出版本领域首部中英文专著《动态精密工程测量》等著作7部，发表SCI论文260余篇，谷歌引用超2万次，H指数76，入选斯坦福大学“终身科学影响力排行榜”。", "type": "text"}]}]	\N	\N	\N	\N	1	2025-07-26 23:21:01.77	2025-07-27 14:12:56.123	2025-07-27 14:12:56.139	1	1	\N	\N	[{"type": "list", "format": "unordered", "children": [{"type": "list-item", "children": [{"text": "动态精密工程测量创新理论和自主装备研究", "type": "text"}]}]}]
27	m2hu7982crlsipuucfbx5r5h	高琦丽	助理教授	Mentor	<EMAIL>	\N	https://www.gaoqili.cn	[{"type": "paragraph", "children": [{"text": "Dr. Qili Gao is an Assistant Professor at Shenzhen University. She received her Ph.D. degree from Wuhan University, China. Her research interests include spatial-temporal data mining, urban informatics, and human mobility and social inequality analysis for urban and transport planning. She has published several papers on human mobility patterns, jobs-housing relationships, socio-spatial segregation and transport-based inequality. She was also awarded the National Natural Science Foundation of China for Youth and the Postdoctoral Science Foundation of China.", "type": "text"}]}]	\N	\N	\N	\N	3	2025-07-27 00:49:12.809	2025-07-27 14:14:01.101	2025-07-27 14:14:01.12	1	1	\N	[{"type": "code", "children": [{"text": "2019    PhD in Geographic Information Science\\n        Wuhan University\\n2015    MEng in Survey and Mapping Engineering\\n        Wuhan University\\n2013    BSc in Remote Sensing Science and Technology\\n        Wuhan University", "type": "text"}], "language": "plaintext"}]	[{"type": "list", "format": "unordered", "children": [{"type": "list-item", "children": [{"text": "Human Dynamics and Urban Informatics", "type": "text"}]}]}, {"type": "list", "format": "unordered", "children": [{"type": "list-item", "children": [{"text": "Spatial Data Mining and Social Computing", "type": "text"}]}]}, {"type": "list", "format": "unordered", "children": [{"type": "list-item", "children": [{"text": "Human Mobility and Social Inequality", "type": "text"}]}]}, {"type": "list", "format": "unordered", "children": [{"type": "list-item", "children": [{"text": "Data-driven Urban Analytics", "type": "text"}]}]}, {"type": "list", "format": "unordered", "children": [{"type": "list-item", "children": [{"text": "Economic Geography and Fintech", "type": "text"}]}]}]
28	e61kok6wq4dqg3o05xue660j	王子阳	硕士在读	Master	<EMAIL>	\N	\N	\N	2024	\N	\N	\N	\N	2025-07-27 01:03:36.766	2025-07-27 14:15:14.248	2025-07-27 14:15:14.262	1	1	\N	\N	[{"type": "paragraph", "children": [{"text": "待定", "type": "text"}]}]
29	kikg8c7x5p41g5fwffe7khvr	王隆	硕士在读	Master	<EMAIL>	\N	\N	\N	2024	\N	\N	\N	\N	2025-07-27 01:00:53.082	2025-07-27 14:15:23.241	2025-07-27 14:15:23.278	1	1	\N	\N	[{"type": "paragraph", "children": [{"text": "待定", "type": "text"}]}]
33	ttl7yao99989tjh1p69tgib3	曹劲舟	助理教授	Collaborator	<EMAIL>	\N	https://www.caojz.cn	[{"type": "paragraph", "children": [{"text": "Jinzhou Cao is an Assistant Professor at the Shenzhen Technology University(SZTU) with the College of Big Data and Internet. Previously, he worked at the Guangdong Key Laboratory of Urban Informatics at the Shenzhen University as a Postdoctoral Researcher and Research Associate.He earned his Ph.D. in GIS from State Key Laboratory of Information Engineering in Surveying, Mapping and Remote Sensing (LIESMARS), Wuhan University. He was also a visiting student of University of Washington, Seattle, USA. during his Ph.D. student period. His research interest and expertise areas include Urban Big Data Mining, Geo-AI and Urban Analytics on the Interaction between Human Mobility and Urban Envronments. He has published over 20 peer-reviewed papers in activity-based urban functions, individual mobility patterns, urban mobility networks and urban mobility scalings. His works have been cited over 400 times according to Google Scholar. He has lead and participated in several research grants funded by NSFC, PSFC, etc.", "type": "text"}]}]	\N	\N	\N	\N	2	2025-07-28 20:55:01.518	2025-07-29 23:03:20.112	\N	1	1	\N	[{"type": "code", "children": [{"text": "2013-2019    PhD in Geographic Information Science\\n             LIESMARS, Wuhan University\\n2017-2018    Joint Ph.D. in THINK LAB\\n             School of Civil and Environmental Engineering, University of Washington\\n2009-2013    BSc in Remote Sensing Science and Technology\\n             School of Remote Sensing and Information Engineering, Wuhan University", "type": "text"}], "language": "plaintext"}, {"type": "paragraph", "children": [{"text": "", "type": "text"}]}]	[{"type": "list", "format": "unordered", "children": [{"type": "list-item", "children": [{"text": "Urban Data Science and Urban Analytics", "type": "text"}]}]}, {"type": "list", "format": "unordered", "children": [{"type": "list-item", "children": [{"text": "Geo-computation and Smart cities", "type": "text"}]}]}, {"type": "list", "format": "unordered", "children": [{"type": "list-item", "children": [{"text": "Human Mobility and Urban Informatics", "type": "text"}]}]}, {"type": "list", "format": "unordered", "children": [{"type": "list-item", "children": [{"text": "Human-Urban-Social Interaction", "type": "text"}]}]}]
35	crq1tx3ovzoanr8i0anan8nw	曹瑞	助理教授	Collaborator	<EMAIL>	\N	https://caorui.space	[{"type": "paragraph", "children": [{"text": "Dr. Rui Cao is currently an Assistant Professor at the Thrust of Urban Governance and Design, The Hong Kong University of Science and Technology (Guangzhou) where he leads the Responsible Urban Intelligence (RUI) Lab. He is specialized in GIScience and particularly interested in GeoAI and Urban Informatics, with an ultimate goal of contributing to sustainable and human-centered smart cities for the benefit of society. He has published extensively on the topic of urban sensing and analytics based on multi-source spatio-temporal big data. He has led and participated in several research grants funded by NSFC, Microsoft, Tencent, etc. He is a member of ACM SIGSPATIAL, CCF, CPGIS, IEEE, and ISUI, and has served as a reviewer for NSFC and BELSPO grants, as well as many prestigious international journals. He served as the conference secretary for the Global Smart Cities Summit cum The 3rd International Conference on Urban Informatics (GSCS&ICUI 2023).", "type": "text"}]}]	\N	\N	\N	\N	3	2025-07-28 21:02:33.247	2025-07-29 23:03:27.016	\N	1	1	\N	[{"type": "code", "children": [{"text": "PhD in Computer Science\\nThe University of Nottingham, UK\\n\\nMEng in GIS\\nWuhan University, China\\n\\nBEng in Geomatics\\nWuhan University, China", "type": "text"}], "language": "plaintext"}]	[{"type": "list", "format": "unordered", "children": [{"type": "list-item", "children": [{"text": "GeoAI & Spatial Data Science", "type": "text"}]}]}, {"type": "list", "format": "unordered", "children": [{"type": "list-item", "children": [{"text": "Urban Informatics & Smart Cities", "type": "text"}]}]}, {"type": "list", "format": "unordered", "children": [{"type": "list-item", "children": [{"text": "Urban Sustainability & Resilience", "type": "text"}]}]}]
31	xesc1duh7a2wmeg1mo1n269c	钟晨	副教授	Collaborator	<EMAIL>	\N	https://imzhongchen.wordpress.com	[{"type": "paragraph", "children": [{"text": "Chen is an Associate Professor of Urban Analytics at the Centre for Advanced Spatial Analysis (CASA), University College London (UCL). She has a background in Geographic Information Systems (GIS) engineering and obtained a PhD from the Architecture Department at ETH Zurich. Her work is characterised by a strong methodological focus on the use of emerging forms of human location data, combined with advanced spatial analysis and modelling. She applies these techniques to investigate critical transport challenges, including socio-spatial inequalities, sustainable mobility, and decarbonisation. Her research is internationally oriented, addressing both theoretical and applied questions across diverse urban contexts.", "type": "text"}]}]	\N	\N	\N	\N	1	2025-07-28 20:47:11.4	2025-07-29 23:02:56.094	\N	1	1	\N	\N	[{"type": "list", "format": "unordered", "children": [{"type": "list-item", "children": [{"text": "Spatial data mining and machine learning for urban analytics", "type": "text"}]}, {"type": "list-item", "children": [{"text": "Urban mobility and travel behavior modeling", "type": "text"}]}, {"type": "list-item", "children": [{"text": "Utilization of emerging big data  in spatial analysis", "type": "text"}]}, {"type": "list-item", "children": [{"text": "Socio-spatial inequality and transport justice", "type": "text"}]}]}]
37	xesc1duh7a2wmeg1mo1n269c	钟晨	副教授	Collaborator	<EMAIL>	\N	https://imzhongchen.wordpress.com	[{"type": "paragraph", "children": [{"text": "Chen is an Associate Professor of Urban Analytics at the Centre for Advanced Spatial Analysis (CASA), University College London (UCL). She has a background in Geographic Information Systems (GIS) engineering and obtained a PhD from the Architecture Department at ETH Zurich. Her work is characterised by a strong methodological focus on the use of emerging forms of human location data, combined with advanced spatial analysis and modelling. She applies these techniques to investigate critical transport challenges, including socio-spatial inequalities, sustainable mobility, and decarbonisation. Her research is internationally oriented, addressing both theoretical and applied questions across diverse urban contexts.", "type": "text"}]}]	\N	\N	\N	\N	1	2025-07-28 20:47:11.4	2025-07-29 23:02:56.094	2025-07-29 23:02:56.115	1	1	\N	\N	[{"type": "list", "format": "unordered", "children": [{"type": "list-item", "children": [{"text": "Spatial data mining and machine learning for urban analytics", "type": "text"}]}, {"type": "list-item", "children": [{"text": "Urban mobility and travel behavior modeling", "type": "text"}]}, {"type": "list-item", "children": [{"text": "Utilization of emerging big data  in spatial analysis", "type": "text"}]}, {"type": "list-item", "children": [{"text": "Socio-spatial inequality and transport justice", "type": "text"}]}]}]
38	ttl7yao99989tjh1p69tgib3	曹劲舟	助理教授	Collaborator	<EMAIL>	\N	https://www.caojz.cn	[{"type": "paragraph", "children": [{"text": "Jinzhou Cao is an Assistant Professor at the Shenzhen Technology University(SZTU) with the College of Big Data and Internet. Previously, he worked at the Guangdong Key Laboratory of Urban Informatics at the Shenzhen University as a Postdoctoral Researcher and Research Associate.He earned his Ph.D. in GIS from State Key Laboratory of Information Engineering in Surveying, Mapping and Remote Sensing (LIESMARS), Wuhan University. He was also a visiting student of University of Washington, Seattle, USA. during his Ph.D. student period. His research interest and expertise areas include Urban Big Data Mining, Geo-AI and Urban Analytics on the Interaction between Human Mobility and Urban Envronments. He has published over 20 peer-reviewed papers in activity-based urban functions, individual mobility patterns, urban mobility networks and urban mobility scalings. His works have been cited over 400 times according to Google Scholar. He has lead and participated in several research grants funded by NSFC, PSFC, etc.", "type": "text"}]}]	\N	\N	\N	\N	2	2025-07-28 20:55:01.518	2025-07-29 23:03:20.112	2025-07-29 23:03:20.142	1	1	\N	[{"type": "code", "children": [{"text": "2013-2019    PhD in Geographic Information Science\\n             LIESMARS, Wuhan University\\n2017-2018    Joint Ph.D. in THINK LAB\\n             School of Civil and Environmental Engineering, University of Washington\\n2009-2013    BSc in Remote Sensing Science and Technology\\n             School of Remote Sensing and Information Engineering, Wuhan University", "type": "text"}], "language": "plaintext"}, {"type": "paragraph", "children": [{"text": "", "type": "text"}]}]	[{"type": "list", "format": "unordered", "children": [{"type": "list-item", "children": [{"text": "Urban Data Science and Urban Analytics", "type": "text"}]}]}, {"type": "list", "format": "unordered", "children": [{"type": "list-item", "children": [{"text": "Geo-computation and Smart cities", "type": "text"}]}]}, {"type": "list", "format": "unordered", "children": [{"type": "list-item", "children": [{"text": "Human Mobility and Urban Informatics", "type": "text"}]}]}, {"type": "list", "format": "unordered", "children": [{"type": "list-item", "children": [{"text": "Human-Urban-Social Interaction", "type": "text"}]}]}]
39	crq1tx3ovzoanr8i0anan8nw	曹瑞	助理教授	Collaborator	<EMAIL>	\N	https://caorui.space	[{"type": "paragraph", "children": [{"text": "Dr. Rui Cao is currently an Assistant Professor at the Thrust of Urban Governance and Design, The Hong Kong University of Science and Technology (Guangzhou) where he leads the Responsible Urban Intelligence (RUI) Lab. He is specialized in GIScience and particularly interested in GeoAI and Urban Informatics, with an ultimate goal of contributing to sustainable and human-centered smart cities for the benefit of society. He has published extensively on the topic of urban sensing and analytics based on multi-source spatio-temporal big data. He has led and participated in several research grants funded by NSFC, Microsoft, Tencent, etc. He is a member of ACM SIGSPATIAL, CCF, CPGIS, IEEE, and ISUI, and has served as a reviewer for NSFC and BELSPO grants, as well as many prestigious international journals. He served as the conference secretary for the Global Smart Cities Summit cum The 3rd International Conference on Urban Informatics (GSCS&ICUI 2023).", "type": "text"}]}]	\N	\N	\N	\N	3	2025-07-28 21:02:33.247	2025-07-29 23:03:27.016	2025-07-29 23:03:27.04	1	1	\N	[{"type": "code", "children": [{"text": "PhD in Computer Science\\nThe University of Nottingham, UK\\n\\nMEng in GIS\\nWuhan University, China\\n\\nBEng in Geomatics\\nWuhan University, China", "type": "text"}], "language": "plaintext"}]	[{"type": "list", "format": "unordered", "children": [{"type": "list-item", "children": [{"text": "GeoAI & Spatial Data Science", "type": "text"}]}]}, {"type": "list", "format": "unordered", "children": [{"type": "list-item", "children": [{"text": "Urban Informatics & Smart Cities", "type": "text"}]}]}, {"type": "list", "format": "unordered", "children": [{"type": "list-item", "children": [{"text": "Urban Sustainability & Resilience", "type": "text"}]}]}]
\.


--
-- Data for Name: up_permissions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.up_permissions (id, document_id, action, created_at, updated_at, published_at, created_by_id, updated_by_id, locale) FROM stdin;
1	mmb60j2br18b6nns1i1nfudr	plugin::users-permissions.user.me	2025-07-20 15:48:25.862	2025-07-20 15:48:25.862	2025-07-20 15:48:25.862	\N	\N	\N
2	lctdymw0chai709aoeau2zlg	plugin::users-permissions.auth.changePassword	2025-07-20 15:48:25.862	2025-07-20 15:48:25.862	2025-07-20 15:48:25.863	\N	\N	\N
3	sg7amdydbowtyhddhkcnpik6	plugin::users-permissions.auth.callback	2025-07-20 15:48:25.874	2025-07-20 15:48:25.874	2025-07-20 15:48:25.874	\N	\N	\N
4	knybn0tvxsadyy9977rggcr7	plugin::users-permissions.auth.register	2025-07-20 15:48:25.874	2025-07-20 15:48:25.874	2025-07-20 15:48:25.875	\N	\N	\N
5	l9wd7r48fc4twv52zur92ijr	plugin::users-permissions.auth.forgotPassword	2025-07-20 15:48:25.874	2025-07-20 15:48:25.874	2025-07-20 15:48:25.875	\N	\N	\N
7	dtr7mvb1fqhrmyun9c7f3b4z	plugin::users-permissions.auth.connect	2025-07-20 15:48:25.874	2025-07-20 15:48:25.874	2025-07-20 15:48:25.874	\N	\N	\N
6	os9hk6pbzdblcvivljbum7g8	plugin::users-permissions.auth.sendEmailConfirmation	2025-07-20 15:48:25.874	2025-07-20 15:48:25.874	2025-07-20 15:48:25.875	\N	\N	\N
8	eglgsmstddyy1zr8jdoabred	plugin::users-permissions.auth.emailConfirmation	2025-07-20 15:48:25.874	2025-07-20 15:48:25.874	2025-07-20 15:48:25.875	\N	\N	\N
9	u0m6c3wony7dfrtxculkqd73	plugin::users-permissions.auth.resetPassword	2025-07-20 15:48:25.874	2025-07-20 15:48:25.874	2025-07-20 15:48:25.875	\N	\N	\N
10	xmnq25f2cu9ag3q30gernwhe	api::team-member.team-member.find	2025-07-26 23:45:47.024	2025-07-26 23:45:47.024	2025-07-26 23:45:47.025	\N	\N	\N
12	al5kkeedvtk79y3ckeu55wnb	api::paper-info.paper-info.find	2025-07-29 21:58:29.945	2025-07-29 21:58:29.945	2025-07-29 21:58:29.947	\N	\N	\N
\.


--
-- Data for Name: up_permissions_role_lnk; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.up_permissions_role_lnk (id, permission_id, role_id, permission_ord) FROM stdin;
1	2	1	1
7	3	2	1
2	1	1	1
3	4	2	1
4	5	2	1
5	6	2	1
6	8	2	1
8	9	2	1
9	7	2	1
10	10	2	2
12	12	2	3
\.


--
-- Data for Name: up_roles; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.up_roles (id, document_id, name, description, type, created_at, updated_at, published_at, created_by_id, updated_by_id, locale) FROM stdin;
1	v5uxmagei5fgyr6by7glf1b6	Authenticated	Default role given to authenticated user.	authenticated	2025-07-20 15:48:25.847	2025-07-20 15:48:25.847	2025-07-20 15:48:25.847	\N	\N	\N
2	wxntawydwk3ayk1y3thq1nf8	Public	Default role given to unauthenticated user.	public	2025-07-20 15:48:25.852	2025-07-29 21:58:29.938	2025-07-20 15:48:25.853	\N	\N	\N
\.


--
-- Data for Name: up_users; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.up_users (id, document_id, username, email, provider, password, reset_password_token, confirmation_token, confirmed, blocked, created_at, updated_at, published_at, created_by_id, updated_by_id, locale) FROM stdin;
\.


--
-- Data for Name: up_users_role_lnk; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.up_users_role_lnk (id, user_id, role_id, user_ord) FROM stdin;
\.


--
-- Data for Name: upload_folders; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.upload_folders (id, document_id, name, path_id, path, created_at, updated_at, published_at, created_by_id, updated_by_id, locale) FROM stdin;
1	zwfxta9e6ayo364085wnk9yo	team_member	1	/1	2025-07-26 23:18:05.243	2025-07-26 23:18:05.243	2025-07-26 23:18:05.243	1	1	\N
\.


--
-- Data for Name: upload_folders_parent_lnk; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.upload_folders_parent_lnk (id, folder_id, inv_folder_id, folder_ord) FROM stdin;
\.


--
-- Name: admin_permissions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.admin_permissions_id_seq', 100, true);


--
-- Name: admin_permissions_role_lnk_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.admin_permissions_role_lnk_id_seq', 100, true);


--
-- Name: admin_roles_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.admin_roles_id_seq', 3, true);


--
-- Name: admin_users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.admin_users_id_seq', 1, true);


--
-- Name: admin_users_roles_lnk_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.admin_users_roles_lnk_id_seq', 1, true);


--
-- Name: doi_collections_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.doi_collections_id_seq', 94, true);


--
-- Name: files_folder_lnk_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.files_folder_lnk_id_seq', 6, true);


--
-- Name: files_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.files_id_seq', 6, true);


--
-- Name: files_related_mph_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.files_related_mph_id_seq', 50, true);


--
-- Name: i18n_locale_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.i18n_locale_id_seq', 1, true);


--
-- Name: paper_infos_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.paper_infos_id_seq', 55, true);


--
-- Name: strapi_api_token_permissions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.strapi_api_token_permissions_id_seq', 1, false);


--
-- Name: strapi_api_token_permissions_token_lnk_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.strapi_api_token_permissions_token_lnk_id_seq', 1, false);


--
-- Name: strapi_api_tokens_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.strapi_api_tokens_id_seq', 2, true);


--
-- Name: strapi_core_store_settings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.strapi_core_store_settings_id_seq', 29, true);


--
-- Name: strapi_database_schema_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.strapi_database_schema_id_seq', 12, true);


--
-- Name: strapi_history_versions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.strapi_history_versions_id_seq', 1, false);


--
-- Name: strapi_migrations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.strapi_migrations_id_seq', 1, false);


--
-- Name: strapi_migrations_internal_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.strapi_migrations_internal_id_seq', 6, true);


--
-- Name: strapi_release_actions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.strapi_release_actions_id_seq', 1, false);


--
-- Name: strapi_release_actions_release_lnk_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.strapi_release_actions_release_lnk_id_seq', 1, false);


--
-- Name: strapi_releases_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.strapi_releases_id_seq', 1, false);


--
-- Name: strapi_transfer_token_permissions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.strapi_transfer_token_permissions_id_seq', 1, false);


--
-- Name: strapi_transfer_token_permissions_token_lnk_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.strapi_transfer_token_permissions_token_lnk_id_seq', 1, false);


--
-- Name: strapi_transfer_tokens_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.strapi_transfer_tokens_id_seq', 1, false);


--
-- Name: strapi_webhooks_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.strapi_webhooks_id_seq', 1, false);


--
-- Name: strapi_workflows_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.strapi_workflows_id_seq', 1, false);


--
-- Name: strapi_workflows_stage_required_to_publish_lnk_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.strapi_workflows_stage_required_to_publish_lnk_id_seq', 1, false);


--
-- Name: strapi_workflows_stages_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.strapi_workflows_stages_id_seq', 1, false);


--
-- Name: strapi_workflows_stages_permissions_lnk_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.strapi_workflows_stages_permissions_lnk_id_seq', 1, false);


--
-- Name: strapi_workflows_stages_workflow_lnk_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.strapi_workflows_stages_workflow_lnk_id_seq', 1, false);


--
-- Name: team_members_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.team_members_id_seq', 39, true);


--
-- Name: up_permissions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.up_permissions_id_seq', 12, true);


--
-- Name: up_permissions_role_lnk_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.up_permissions_role_lnk_id_seq', 12, true);


--
-- Name: up_roles_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.up_roles_id_seq', 3, true);


--
-- Name: up_users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.up_users_id_seq', 1, false);


--
-- Name: up_users_role_lnk_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.up_users_role_lnk_id_seq', 1, false);


--
-- Name: upload_folders_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.upload_folders_id_seq', 1, true);


--
-- Name: upload_folders_parent_lnk_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.upload_folders_parent_lnk_id_seq', 1, false);


--
-- Name: admin_permissions admin_permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.admin_permissions
    ADD CONSTRAINT admin_permissions_pkey PRIMARY KEY (id);


--
-- Name: admin_permissions_role_lnk admin_permissions_role_lnk_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.admin_permissions_role_lnk
    ADD CONSTRAINT admin_permissions_role_lnk_pkey PRIMARY KEY (id);


--
-- Name: admin_permissions_role_lnk admin_permissions_role_lnk_uq; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.admin_permissions_role_lnk
    ADD CONSTRAINT admin_permissions_role_lnk_uq UNIQUE (permission_id, role_id);


--
-- Name: admin_roles admin_roles_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.admin_roles
    ADD CONSTRAINT admin_roles_pkey PRIMARY KEY (id);


--
-- Name: admin_users admin_users_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.admin_users
    ADD CONSTRAINT admin_users_pkey PRIMARY KEY (id);


--
-- Name: admin_users_roles_lnk admin_users_roles_lnk_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.admin_users_roles_lnk
    ADD CONSTRAINT admin_users_roles_lnk_pkey PRIMARY KEY (id);


--
-- Name: admin_users_roles_lnk admin_users_roles_lnk_uq; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.admin_users_roles_lnk
    ADD CONSTRAINT admin_users_roles_lnk_uq UNIQUE (user_id, role_id);


--
-- Name: doi_collections doi_collections_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.doi_collections
    ADD CONSTRAINT doi_collections_pkey PRIMARY KEY (id);


--
-- Name: files_folder_lnk files_folder_lnk_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.files_folder_lnk
    ADD CONSTRAINT files_folder_lnk_pkey PRIMARY KEY (id);


--
-- Name: files_folder_lnk files_folder_lnk_uq; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.files_folder_lnk
    ADD CONSTRAINT files_folder_lnk_uq UNIQUE (file_id, folder_id);


--
-- Name: files files_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.files
    ADD CONSTRAINT files_pkey PRIMARY KEY (id);


--
-- Name: files_related_mph files_related_mph_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.files_related_mph
    ADD CONSTRAINT files_related_mph_pkey PRIMARY KEY (id);


--
-- Name: i18n_locale i18n_locale_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.i18n_locale
    ADD CONSTRAINT i18n_locale_pkey PRIMARY KEY (id);


--
-- Name: paper_infos paper_infos_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.paper_infos
    ADD CONSTRAINT paper_infos_pkey PRIMARY KEY (id);


--
-- Name: strapi_api_token_permissions strapi_api_token_permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_api_token_permissions
    ADD CONSTRAINT strapi_api_token_permissions_pkey PRIMARY KEY (id);


--
-- Name: strapi_api_token_permissions_token_lnk strapi_api_token_permissions_token_lnk_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_api_token_permissions_token_lnk
    ADD CONSTRAINT strapi_api_token_permissions_token_lnk_pkey PRIMARY KEY (id);


--
-- Name: strapi_api_token_permissions_token_lnk strapi_api_token_permissions_token_lnk_uq; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_api_token_permissions_token_lnk
    ADD CONSTRAINT strapi_api_token_permissions_token_lnk_uq UNIQUE (api_token_permission_id, api_token_id);


--
-- Name: strapi_api_tokens strapi_api_tokens_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_api_tokens
    ADD CONSTRAINT strapi_api_tokens_pkey PRIMARY KEY (id);


--
-- Name: strapi_core_store_settings strapi_core_store_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_core_store_settings
    ADD CONSTRAINT strapi_core_store_settings_pkey PRIMARY KEY (id);


--
-- Name: strapi_database_schema strapi_database_schema_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_database_schema
    ADD CONSTRAINT strapi_database_schema_pkey PRIMARY KEY (id);


--
-- Name: strapi_history_versions strapi_history_versions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_history_versions
    ADD CONSTRAINT strapi_history_versions_pkey PRIMARY KEY (id);


--
-- Name: strapi_migrations_internal strapi_migrations_internal_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_migrations_internal
    ADD CONSTRAINT strapi_migrations_internal_pkey PRIMARY KEY (id);


--
-- Name: strapi_migrations strapi_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_migrations
    ADD CONSTRAINT strapi_migrations_pkey PRIMARY KEY (id);


--
-- Name: strapi_release_actions strapi_release_actions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_release_actions
    ADD CONSTRAINT strapi_release_actions_pkey PRIMARY KEY (id);


--
-- Name: strapi_release_actions_release_lnk strapi_release_actions_release_lnk_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_release_actions_release_lnk
    ADD CONSTRAINT strapi_release_actions_release_lnk_pkey PRIMARY KEY (id);


--
-- Name: strapi_release_actions_release_lnk strapi_release_actions_release_lnk_uq; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_release_actions_release_lnk
    ADD CONSTRAINT strapi_release_actions_release_lnk_uq UNIQUE (release_action_id, release_id);


--
-- Name: strapi_releases strapi_releases_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_releases
    ADD CONSTRAINT strapi_releases_pkey PRIMARY KEY (id);


--
-- Name: strapi_transfer_token_permissions strapi_transfer_token_permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_transfer_token_permissions
    ADD CONSTRAINT strapi_transfer_token_permissions_pkey PRIMARY KEY (id);


--
-- Name: strapi_transfer_token_permissions_token_lnk strapi_transfer_token_permissions_token_lnk_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_transfer_token_permissions_token_lnk
    ADD CONSTRAINT strapi_transfer_token_permissions_token_lnk_pkey PRIMARY KEY (id);


--
-- Name: strapi_transfer_token_permissions_token_lnk strapi_transfer_token_permissions_token_lnk_uq; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_transfer_token_permissions_token_lnk
    ADD CONSTRAINT strapi_transfer_token_permissions_token_lnk_uq UNIQUE (transfer_token_permission_id, transfer_token_id);


--
-- Name: strapi_transfer_tokens strapi_transfer_tokens_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_transfer_tokens
    ADD CONSTRAINT strapi_transfer_tokens_pkey PRIMARY KEY (id);


--
-- Name: strapi_webhooks strapi_webhooks_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_webhooks
    ADD CONSTRAINT strapi_webhooks_pkey PRIMARY KEY (id);


--
-- Name: strapi_workflows strapi_workflows_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_workflows
    ADD CONSTRAINT strapi_workflows_pkey PRIMARY KEY (id);


--
-- Name: strapi_workflows_stage_required_to_publish_lnk strapi_workflows_stage_required_to_publish_lnk_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_workflows_stage_required_to_publish_lnk
    ADD CONSTRAINT strapi_workflows_stage_required_to_publish_lnk_pkey PRIMARY KEY (id);


--
-- Name: strapi_workflows_stage_required_to_publish_lnk strapi_workflows_stage_required_to_publish_lnk_uq; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_workflows_stage_required_to_publish_lnk
    ADD CONSTRAINT strapi_workflows_stage_required_to_publish_lnk_uq UNIQUE (workflow_id, workflow_stage_id);


--
-- Name: strapi_workflows_stages_permissions_lnk strapi_workflows_stages_permissions_lnk_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_workflows_stages_permissions_lnk
    ADD CONSTRAINT strapi_workflows_stages_permissions_lnk_pkey PRIMARY KEY (id);


--
-- Name: strapi_workflows_stages_permissions_lnk strapi_workflows_stages_permissions_lnk_uq; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_workflows_stages_permissions_lnk
    ADD CONSTRAINT strapi_workflows_stages_permissions_lnk_uq UNIQUE (workflow_stage_id, permission_id);


--
-- Name: strapi_workflows_stages strapi_workflows_stages_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_workflows_stages
    ADD CONSTRAINT strapi_workflows_stages_pkey PRIMARY KEY (id);


--
-- Name: strapi_workflows_stages_workflow_lnk strapi_workflows_stages_workflow_lnk_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_workflows_stages_workflow_lnk
    ADD CONSTRAINT strapi_workflows_stages_workflow_lnk_pkey PRIMARY KEY (id);


--
-- Name: strapi_workflows_stages_workflow_lnk strapi_workflows_stages_workflow_lnk_uq; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_workflows_stages_workflow_lnk
    ADD CONSTRAINT strapi_workflows_stages_workflow_lnk_uq UNIQUE (workflow_stage_id, workflow_id);


--
-- Name: team_members team_members_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.team_members
    ADD CONSTRAINT team_members_pkey PRIMARY KEY (id);


--
-- Name: up_permissions up_permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.up_permissions
    ADD CONSTRAINT up_permissions_pkey PRIMARY KEY (id);


--
-- Name: up_permissions_role_lnk up_permissions_role_lnk_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.up_permissions_role_lnk
    ADD CONSTRAINT up_permissions_role_lnk_pkey PRIMARY KEY (id);


--
-- Name: up_permissions_role_lnk up_permissions_role_lnk_uq; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.up_permissions_role_lnk
    ADD CONSTRAINT up_permissions_role_lnk_uq UNIQUE (permission_id, role_id);


--
-- Name: up_roles up_roles_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.up_roles
    ADD CONSTRAINT up_roles_pkey PRIMARY KEY (id);


--
-- Name: up_users up_users_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.up_users
    ADD CONSTRAINT up_users_pkey PRIMARY KEY (id);


--
-- Name: up_users_role_lnk up_users_role_lnk_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.up_users_role_lnk
    ADD CONSTRAINT up_users_role_lnk_pkey PRIMARY KEY (id);


--
-- Name: up_users_role_lnk up_users_role_lnk_uq; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.up_users_role_lnk
    ADD CONSTRAINT up_users_role_lnk_uq UNIQUE (user_id, role_id);


--
-- Name: upload_folders_parent_lnk upload_folders_parent_lnk_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.upload_folders_parent_lnk
    ADD CONSTRAINT upload_folders_parent_lnk_pkey PRIMARY KEY (id);


--
-- Name: upload_folders_parent_lnk upload_folders_parent_lnk_uq; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.upload_folders_parent_lnk
    ADD CONSTRAINT upload_folders_parent_lnk_uq UNIQUE (folder_id, inv_folder_id);


--
-- Name: upload_folders upload_folders_path_id_index; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.upload_folders
    ADD CONSTRAINT upload_folders_path_id_index UNIQUE (path_id);


--
-- Name: upload_folders upload_folders_path_index; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.upload_folders
    ADD CONSTRAINT upload_folders_path_index UNIQUE (path);


--
-- Name: upload_folders upload_folders_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.upload_folders
    ADD CONSTRAINT upload_folders_pkey PRIMARY KEY (id);


--
-- Name: admin_permissions_created_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX admin_permissions_created_by_id_fk ON public.admin_permissions USING btree (created_by_id);


--
-- Name: admin_permissions_documents_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX admin_permissions_documents_idx ON public.admin_permissions USING btree (document_id, locale, published_at);


--
-- Name: admin_permissions_role_lnk_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX admin_permissions_role_lnk_fk ON public.admin_permissions_role_lnk USING btree (permission_id);


--
-- Name: admin_permissions_role_lnk_ifk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX admin_permissions_role_lnk_ifk ON public.admin_permissions_role_lnk USING btree (role_id);


--
-- Name: admin_permissions_role_lnk_oifk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX admin_permissions_role_lnk_oifk ON public.admin_permissions_role_lnk USING btree (permission_ord);


--
-- Name: admin_permissions_updated_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX admin_permissions_updated_by_id_fk ON public.admin_permissions USING btree (updated_by_id);


--
-- Name: admin_roles_created_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX admin_roles_created_by_id_fk ON public.admin_roles USING btree (created_by_id);


--
-- Name: admin_roles_documents_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX admin_roles_documents_idx ON public.admin_roles USING btree (document_id, locale, published_at);


--
-- Name: admin_roles_updated_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX admin_roles_updated_by_id_fk ON public.admin_roles USING btree (updated_by_id);


--
-- Name: admin_users_created_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX admin_users_created_by_id_fk ON public.admin_users USING btree (created_by_id);


--
-- Name: admin_users_documents_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX admin_users_documents_idx ON public.admin_users USING btree (document_id, locale, published_at);


--
-- Name: admin_users_roles_lnk_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX admin_users_roles_lnk_fk ON public.admin_users_roles_lnk USING btree (user_id);


--
-- Name: admin_users_roles_lnk_ifk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX admin_users_roles_lnk_ifk ON public.admin_users_roles_lnk USING btree (role_id);


--
-- Name: admin_users_roles_lnk_ofk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX admin_users_roles_lnk_ofk ON public.admin_users_roles_lnk USING btree (role_ord);


--
-- Name: admin_users_roles_lnk_oifk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX admin_users_roles_lnk_oifk ON public.admin_users_roles_lnk USING btree (user_ord);


--
-- Name: admin_users_updated_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX admin_users_updated_by_id_fk ON public.admin_users USING btree (updated_by_id);


--
-- Name: doi_collections_created_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX doi_collections_created_by_id_fk ON public.doi_collections USING btree (created_by_id);


--
-- Name: doi_collections_documents_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX doi_collections_documents_idx ON public.doi_collections USING btree (document_id, locale, published_at);


--
-- Name: doi_collections_updated_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX doi_collections_updated_by_id_fk ON public.doi_collections USING btree (updated_by_id);


--
-- Name: files_created_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX files_created_by_id_fk ON public.files USING btree (created_by_id);


--
-- Name: files_documents_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX files_documents_idx ON public.files USING btree (document_id, locale, published_at);


--
-- Name: files_folder_lnk_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX files_folder_lnk_fk ON public.files_folder_lnk USING btree (file_id);


--
-- Name: files_folder_lnk_ifk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX files_folder_lnk_ifk ON public.files_folder_lnk USING btree (folder_id);


--
-- Name: files_folder_lnk_oifk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX files_folder_lnk_oifk ON public.files_folder_lnk USING btree (file_ord);


--
-- Name: files_related_mph_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX files_related_mph_fk ON public.files_related_mph USING btree (file_id);


--
-- Name: files_related_mph_idix; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX files_related_mph_idix ON public.files_related_mph USING btree (related_id);


--
-- Name: files_related_mph_oidx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX files_related_mph_oidx ON public.files_related_mph USING btree ("order");


--
-- Name: files_updated_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX files_updated_by_id_fk ON public.files USING btree (updated_by_id);


--
-- Name: i18n_locale_created_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX i18n_locale_created_by_id_fk ON public.i18n_locale USING btree (created_by_id);


--
-- Name: i18n_locale_documents_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX i18n_locale_documents_idx ON public.i18n_locale USING btree (document_id, locale, published_at);


--
-- Name: i18n_locale_updated_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX i18n_locale_updated_by_id_fk ON public.i18n_locale USING btree (updated_by_id);


--
-- Name: paper_infos_created_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX paper_infos_created_by_id_fk ON public.paper_infos USING btree (created_by_id);


--
-- Name: paper_infos_documents_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX paper_infos_documents_idx ON public.paper_infos USING btree (document_id, locale, published_at);


--
-- Name: paper_infos_updated_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX paper_infos_updated_by_id_fk ON public.paper_infos USING btree (updated_by_id);


--
-- Name: strapi_api_token_permissions_created_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_api_token_permissions_created_by_id_fk ON public.strapi_api_token_permissions USING btree (created_by_id);


--
-- Name: strapi_api_token_permissions_documents_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_api_token_permissions_documents_idx ON public.strapi_api_token_permissions USING btree (document_id, locale, published_at);


--
-- Name: strapi_api_token_permissions_token_lnk_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_api_token_permissions_token_lnk_fk ON public.strapi_api_token_permissions_token_lnk USING btree (api_token_permission_id);


--
-- Name: strapi_api_token_permissions_token_lnk_ifk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_api_token_permissions_token_lnk_ifk ON public.strapi_api_token_permissions_token_lnk USING btree (api_token_id);


--
-- Name: strapi_api_token_permissions_token_lnk_oifk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_api_token_permissions_token_lnk_oifk ON public.strapi_api_token_permissions_token_lnk USING btree (api_token_permission_ord);


--
-- Name: strapi_api_token_permissions_updated_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_api_token_permissions_updated_by_id_fk ON public.strapi_api_token_permissions USING btree (updated_by_id);


--
-- Name: strapi_api_tokens_created_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_api_tokens_created_by_id_fk ON public.strapi_api_tokens USING btree (created_by_id);


--
-- Name: strapi_api_tokens_documents_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_api_tokens_documents_idx ON public.strapi_api_tokens USING btree (document_id, locale, published_at);


--
-- Name: strapi_api_tokens_updated_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_api_tokens_updated_by_id_fk ON public.strapi_api_tokens USING btree (updated_by_id);


--
-- Name: strapi_history_versions_created_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_history_versions_created_by_id_fk ON public.strapi_history_versions USING btree (created_by_id);


--
-- Name: strapi_release_actions_created_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_release_actions_created_by_id_fk ON public.strapi_release_actions USING btree (created_by_id);


--
-- Name: strapi_release_actions_documents_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_release_actions_documents_idx ON public.strapi_release_actions USING btree (document_id, locale, published_at);


--
-- Name: strapi_release_actions_release_lnk_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_release_actions_release_lnk_fk ON public.strapi_release_actions_release_lnk USING btree (release_action_id);


--
-- Name: strapi_release_actions_release_lnk_ifk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_release_actions_release_lnk_ifk ON public.strapi_release_actions_release_lnk USING btree (release_id);


--
-- Name: strapi_release_actions_release_lnk_oifk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_release_actions_release_lnk_oifk ON public.strapi_release_actions_release_lnk USING btree (release_action_ord);


--
-- Name: strapi_release_actions_updated_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_release_actions_updated_by_id_fk ON public.strapi_release_actions USING btree (updated_by_id);


--
-- Name: strapi_releases_created_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_releases_created_by_id_fk ON public.strapi_releases USING btree (created_by_id);


--
-- Name: strapi_releases_documents_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_releases_documents_idx ON public.strapi_releases USING btree (document_id, locale, published_at);


--
-- Name: strapi_releases_updated_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_releases_updated_by_id_fk ON public.strapi_releases USING btree (updated_by_id);


--
-- Name: strapi_transfer_token_permissions_created_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_transfer_token_permissions_created_by_id_fk ON public.strapi_transfer_token_permissions USING btree (created_by_id);


--
-- Name: strapi_transfer_token_permissions_documents_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_transfer_token_permissions_documents_idx ON public.strapi_transfer_token_permissions USING btree (document_id, locale, published_at);


--
-- Name: strapi_transfer_token_permissions_token_lnk_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_transfer_token_permissions_token_lnk_fk ON public.strapi_transfer_token_permissions_token_lnk USING btree (transfer_token_permission_id);


--
-- Name: strapi_transfer_token_permissions_token_lnk_ifk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_transfer_token_permissions_token_lnk_ifk ON public.strapi_transfer_token_permissions_token_lnk USING btree (transfer_token_id);


--
-- Name: strapi_transfer_token_permissions_token_lnk_oifk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_transfer_token_permissions_token_lnk_oifk ON public.strapi_transfer_token_permissions_token_lnk USING btree (transfer_token_permission_ord);


--
-- Name: strapi_transfer_token_permissions_updated_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_transfer_token_permissions_updated_by_id_fk ON public.strapi_transfer_token_permissions USING btree (updated_by_id);


--
-- Name: strapi_transfer_tokens_created_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_transfer_tokens_created_by_id_fk ON public.strapi_transfer_tokens USING btree (created_by_id);


--
-- Name: strapi_transfer_tokens_documents_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_transfer_tokens_documents_idx ON public.strapi_transfer_tokens USING btree (document_id, locale, published_at);


--
-- Name: strapi_transfer_tokens_updated_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_transfer_tokens_updated_by_id_fk ON public.strapi_transfer_tokens USING btree (updated_by_id);


--
-- Name: strapi_workflows_created_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_workflows_created_by_id_fk ON public.strapi_workflows USING btree (created_by_id);


--
-- Name: strapi_workflows_documents_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_workflows_documents_idx ON public.strapi_workflows USING btree (document_id, locale, published_at);


--
-- Name: strapi_workflows_stage_required_to_publish_lnk_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_workflows_stage_required_to_publish_lnk_fk ON public.strapi_workflows_stage_required_to_publish_lnk USING btree (workflow_id);


--
-- Name: strapi_workflows_stage_required_to_publish_lnk_ifk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_workflows_stage_required_to_publish_lnk_ifk ON public.strapi_workflows_stage_required_to_publish_lnk USING btree (workflow_stage_id);


--
-- Name: strapi_workflows_stages_created_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_workflows_stages_created_by_id_fk ON public.strapi_workflows_stages USING btree (created_by_id);


--
-- Name: strapi_workflows_stages_documents_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_workflows_stages_documents_idx ON public.strapi_workflows_stages USING btree (document_id, locale, published_at);


--
-- Name: strapi_workflows_stages_permissions_lnk_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_workflows_stages_permissions_lnk_fk ON public.strapi_workflows_stages_permissions_lnk USING btree (workflow_stage_id);


--
-- Name: strapi_workflows_stages_permissions_lnk_ifk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_workflows_stages_permissions_lnk_ifk ON public.strapi_workflows_stages_permissions_lnk USING btree (permission_id);


--
-- Name: strapi_workflows_stages_permissions_lnk_ofk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_workflows_stages_permissions_lnk_ofk ON public.strapi_workflows_stages_permissions_lnk USING btree (permission_ord);


--
-- Name: strapi_workflows_stages_updated_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_workflows_stages_updated_by_id_fk ON public.strapi_workflows_stages USING btree (updated_by_id);


--
-- Name: strapi_workflows_stages_workflow_lnk_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_workflows_stages_workflow_lnk_fk ON public.strapi_workflows_stages_workflow_lnk USING btree (workflow_stage_id);


--
-- Name: strapi_workflows_stages_workflow_lnk_ifk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_workflows_stages_workflow_lnk_ifk ON public.strapi_workflows_stages_workflow_lnk USING btree (workflow_id);


--
-- Name: strapi_workflows_stages_workflow_lnk_oifk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_workflows_stages_workflow_lnk_oifk ON public.strapi_workflows_stages_workflow_lnk USING btree (workflow_stage_ord);


--
-- Name: strapi_workflows_updated_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX strapi_workflows_updated_by_id_fk ON public.strapi_workflows USING btree (updated_by_id);


--
-- Name: team_members_created_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX team_members_created_by_id_fk ON public.team_members USING btree (created_by_id);


--
-- Name: team_members_documents_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX team_members_documents_idx ON public.team_members USING btree (document_id, locale, published_at);


--
-- Name: team_members_updated_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX team_members_updated_by_id_fk ON public.team_members USING btree (updated_by_id);


--
-- Name: up_permissions_created_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX up_permissions_created_by_id_fk ON public.up_permissions USING btree (created_by_id);


--
-- Name: up_permissions_documents_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX up_permissions_documents_idx ON public.up_permissions USING btree (document_id, locale, published_at);


--
-- Name: up_permissions_role_lnk_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX up_permissions_role_lnk_fk ON public.up_permissions_role_lnk USING btree (permission_id);


--
-- Name: up_permissions_role_lnk_ifk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX up_permissions_role_lnk_ifk ON public.up_permissions_role_lnk USING btree (role_id);


--
-- Name: up_permissions_role_lnk_oifk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX up_permissions_role_lnk_oifk ON public.up_permissions_role_lnk USING btree (permission_ord);


--
-- Name: up_permissions_updated_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX up_permissions_updated_by_id_fk ON public.up_permissions USING btree (updated_by_id);


--
-- Name: up_roles_created_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX up_roles_created_by_id_fk ON public.up_roles USING btree (created_by_id);


--
-- Name: up_roles_documents_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX up_roles_documents_idx ON public.up_roles USING btree (document_id, locale, published_at);


--
-- Name: up_roles_updated_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX up_roles_updated_by_id_fk ON public.up_roles USING btree (updated_by_id);


--
-- Name: up_users_created_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX up_users_created_by_id_fk ON public.up_users USING btree (created_by_id);


--
-- Name: up_users_documents_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX up_users_documents_idx ON public.up_users USING btree (document_id, locale, published_at);


--
-- Name: up_users_role_lnk_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX up_users_role_lnk_fk ON public.up_users_role_lnk USING btree (user_id);


--
-- Name: up_users_role_lnk_ifk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX up_users_role_lnk_ifk ON public.up_users_role_lnk USING btree (role_id);


--
-- Name: up_users_role_lnk_oifk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX up_users_role_lnk_oifk ON public.up_users_role_lnk USING btree (user_ord);


--
-- Name: up_users_updated_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX up_users_updated_by_id_fk ON public.up_users USING btree (updated_by_id);


--
-- Name: upload_files_created_at_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX upload_files_created_at_index ON public.files USING btree (created_at);


--
-- Name: upload_files_ext_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX upload_files_ext_index ON public.files USING btree (ext);


--
-- Name: upload_files_folder_path_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX upload_files_folder_path_index ON public.files USING btree (folder_path);


--
-- Name: upload_files_name_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX upload_files_name_index ON public.files USING btree (name);


--
-- Name: upload_files_size_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX upload_files_size_index ON public.files USING btree (size);


--
-- Name: upload_files_updated_at_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX upload_files_updated_at_index ON public.files USING btree (updated_at);


--
-- Name: upload_folders_created_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX upload_folders_created_by_id_fk ON public.upload_folders USING btree (created_by_id);


--
-- Name: upload_folders_documents_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX upload_folders_documents_idx ON public.upload_folders USING btree (document_id, locale, published_at);


--
-- Name: upload_folders_parent_lnk_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX upload_folders_parent_lnk_fk ON public.upload_folders_parent_lnk USING btree (folder_id);


--
-- Name: upload_folders_parent_lnk_ifk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX upload_folders_parent_lnk_ifk ON public.upload_folders_parent_lnk USING btree (inv_folder_id);


--
-- Name: upload_folders_parent_lnk_oifk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX upload_folders_parent_lnk_oifk ON public.upload_folders_parent_lnk USING btree (folder_ord);


--
-- Name: upload_folders_updated_by_id_fk; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX upload_folders_updated_by_id_fk ON public.upload_folders USING btree (updated_by_id);


--
-- Name: admin_permissions admin_permissions_created_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.admin_permissions
    ADD CONSTRAINT admin_permissions_created_by_id_fk FOREIGN KEY (created_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: admin_permissions_role_lnk admin_permissions_role_lnk_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.admin_permissions_role_lnk
    ADD CONSTRAINT admin_permissions_role_lnk_fk FOREIGN KEY (permission_id) REFERENCES public.admin_permissions(id) ON DELETE CASCADE;


--
-- Name: admin_permissions_role_lnk admin_permissions_role_lnk_ifk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.admin_permissions_role_lnk
    ADD CONSTRAINT admin_permissions_role_lnk_ifk FOREIGN KEY (role_id) REFERENCES public.admin_roles(id) ON DELETE CASCADE;


--
-- Name: admin_permissions admin_permissions_updated_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.admin_permissions
    ADD CONSTRAINT admin_permissions_updated_by_id_fk FOREIGN KEY (updated_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: admin_roles admin_roles_created_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.admin_roles
    ADD CONSTRAINT admin_roles_created_by_id_fk FOREIGN KEY (created_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: admin_roles admin_roles_updated_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.admin_roles
    ADD CONSTRAINT admin_roles_updated_by_id_fk FOREIGN KEY (updated_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: admin_users admin_users_created_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.admin_users
    ADD CONSTRAINT admin_users_created_by_id_fk FOREIGN KEY (created_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: admin_users_roles_lnk admin_users_roles_lnk_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.admin_users_roles_lnk
    ADD CONSTRAINT admin_users_roles_lnk_fk FOREIGN KEY (user_id) REFERENCES public.admin_users(id) ON DELETE CASCADE;


--
-- Name: admin_users_roles_lnk admin_users_roles_lnk_ifk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.admin_users_roles_lnk
    ADD CONSTRAINT admin_users_roles_lnk_ifk FOREIGN KEY (role_id) REFERENCES public.admin_roles(id) ON DELETE CASCADE;


--
-- Name: admin_users admin_users_updated_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.admin_users
    ADD CONSTRAINT admin_users_updated_by_id_fk FOREIGN KEY (updated_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: doi_collections doi_collections_created_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.doi_collections
    ADD CONSTRAINT doi_collections_created_by_id_fk FOREIGN KEY (created_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: doi_collections doi_collections_updated_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.doi_collections
    ADD CONSTRAINT doi_collections_updated_by_id_fk FOREIGN KEY (updated_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: files files_created_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.files
    ADD CONSTRAINT files_created_by_id_fk FOREIGN KEY (created_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: files_folder_lnk files_folder_lnk_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.files_folder_lnk
    ADD CONSTRAINT files_folder_lnk_fk FOREIGN KEY (file_id) REFERENCES public.files(id) ON DELETE CASCADE;


--
-- Name: files_folder_lnk files_folder_lnk_ifk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.files_folder_lnk
    ADD CONSTRAINT files_folder_lnk_ifk FOREIGN KEY (folder_id) REFERENCES public.upload_folders(id) ON DELETE CASCADE;


--
-- Name: files_related_mph files_related_mph_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.files_related_mph
    ADD CONSTRAINT files_related_mph_fk FOREIGN KEY (file_id) REFERENCES public.files(id) ON DELETE CASCADE;


--
-- Name: files files_updated_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.files
    ADD CONSTRAINT files_updated_by_id_fk FOREIGN KEY (updated_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: i18n_locale i18n_locale_created_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.i18n_locale
    ADD CONSTRAINT i18n_locale_created_by_id_fk FOREIGN KEY (created_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: i18n_locale i18n_locale_updated_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.i18n_locale
    ADD CONSTRAINT i18n_locale_updated_by_id_fk FOREIGN KEY (updated_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: paper_infos paper_infos_created_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.paper_infos
    ADD CONSTRAINT paper_infos_created_by_id_fk FOREIGN KEY (created_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: paper_infos paper_infos_updated_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.paper_infos
    ADD CONSTRAINT paper_infos_updated_by_id_fk FOREIGN KEY (updated_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: strapi_api_token_permissions strapi_api_token_permissions_created_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_api_token_permissions
    ADD CONSTRAINT strapi_api_token_permissions_created_by_id_fk FOREIGN KEY (created_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: strapi_api_token_permissions_token_lnk strapi_api_token_permissions_token_lnk_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_api_token_permissions_token_lnk
    ADD CONSTRAINT strapi_api_token_permissions_token_lnk_fk FOREIGN KEY (api_token_permission_id) REFERENCES public.strapi_api_token_permissions(id) ON DELETE CASCADE;


--
-- Name: strapi_api_token_permissions_token_lnk strapi_api_token_permissions_token_lnk_ifk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_api_token_permissions_token_lnk
    ADD CONSTRAINT strapi_api_token_permissions_token_lnk_ifk FOREIGN KEY (api_token_id) REFERENCES public.strapi_api_tokens(id) ON DELETE CASCADE;


--
-- Name: strapi_api_token_permissions strapi_api_token_permissions_updated_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_api_token_permissions
    ADD CONSTRAINT strapi_api_token_permissions_updated_by_id_fk FOREIGN KEY (updated_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: strapi_api_tokens strapi_api_tokens_created_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_api_tokens
    ADD CONSTRAINT strapi_api_tokens_created_by_id_fk FOREIGN KEY (created_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: strapi_api_tokens strapi_api_tokens_updated_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_api_tokens
    ADD CONSTRAINT strapi_api_tokens_updated_by_id_fk FOREIGN KEY (updated_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: strapi_history_versions strapi_history_versions_created_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_history_versions
    ADD CONSTRAINT strapi_history_versions_created_by_id_fk FOREIGN KEY (created_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: strapi_release_actions strapi_release_actions_created_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_release_actions
    ADD CONSTRAINT strapi_release_actions_created_by_id_fk FOREIGN KEY (created_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: strapi_release_actions_release_lnk strapi_release_actions_release_lnk_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_release_actions_release_lnk
    ADD CONSTRAINT strapi_release_actions_release_lnk_fk FOREIGN KEY (release_action_id) REFERENCES public.strapi_release_actions(id) ON DELETE CASCADE;


--
-- Name: strapi_release_actions_release_lnk strapi_release_actions_release_lnk_ifk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_release_actions_release_lnk
    ADD CONSTRAINT strapi_release_actions_release_lnk_ifk FOREIGN KEY (release_id) REFERENCES public.strapi_releases(id) ON DELETE CASCADE;


--
-- Name: strapi_release_actions strapi_release_actions_updated_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_release_actions
    ADD CONSTRAINT strapi_release_actions_updated_by_id_fk FOREIGN KEY (updated_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: strapi_releases strapi_releases_created_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_releases
    ADD CONSTRAINT strapi_releases_created_by_id_fk FOREIGN KEY (created_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: strapi_releases strapi_releases_updated_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_releases
    ADD CONSTRAINT strapi_releases_updated_by_id_fk FOREIGN KEY (updated_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: strapi_transfer_token_permissions strapi_transfer_token_permissions_created_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_transfer_token_permissions
    ADD CONSTRAINT strapi_transfer_token_permissions_created_by_id_fk FOREIGN KEY (created_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: strapi_transfer_token_permissions_token_lnk strapi_transfer_token_permissions_token_lnk_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_transfer_token_permissions_token_lnk
    ADD CONSTRAINT strapi_transfer_token_permissions_token_lnk_fk FOREIGN KEY (transfer_token_permission_id) REFERENCES public.strapi_transfer_token_permissions(id) ON DELETE CASCADE;


--
-- Name: strapi_transfer_token_permissions_token_lnk strapi_transfer_token_permissions_token_lnk_ifk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_transfer_token_permissions_token_lnk
    ADD CONSTRAINT strapi_transfer_token_permissions_token_lnk_ifk FOREIGN KEY (transfer_token_id) REFERENCES public.strapi_transfer_tokens(id) ON DELETE CASCADE;


--
-- Name: strapi_transfer_token_permissions strapi_transfer_token_permissions_updated_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_transfer_token_permissions
    ADD CONSTRAINT strapi_transfer_token_permissions_updated_by_id_fk FOREIGN KEY (updated_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: strapi_transfer_tokens strapi_transfer_tokens_created_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_transfer_tokens
    ADD CONSTRAINT strapi_transfer_tokens_created_by_id_fk FOREIGN KEY (created_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: strapi_transfer_tokens strapi_transfer_tokens_updated_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_transfer_tokens
    ADD CONSTRAINT strapi_transfer_tokens_updated_by_id_fk FOREIGN KEY (updated_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: strapi_workflows strapi_workflows_created_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_workflows
    ADD CONSTRAINT strapi_workflows_created_by_id_fk FOREIGN KEY (created_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: strapi_workflows_stage_required_to_publish_lnk strapi_workflows_stage_required_to_publish_lnk_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_workflows_stage_required_to_publish_lnk
    ADD CONSTRAINT strapi_workflows_stage_required_to_publish_lnk_fk FOREIGN KEY (workflow_id) REFERENCES public.strapi_workflows(id) ON DELETE CASCADE;


--
-- Name: strapi_workflows_stage_required_to_publish_lnk strapi_workflows_stage_required_to_publish_lnk_ifk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_workflows_stage_required_to_publish_lnk
    ADD CONSTRAINT strapi_workflows_stage_required_to_publish_lnk_ifk FOREIGN KEY (workflow_stage_id) REFERENCES public.strapi_workflows_stages(id) ON DELETE CASCADE;


--
-- Name: strapi_workflows_stages strapi_workflows_stages_created_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_workflows_stages
    ADD CONSTRAINT strapi_workflows_stages_created_by_id_fk FOREIGN KEY (created_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: strapi_workflows_stages_permissions_lnk strapi_workflows_stages_permissions_lnk_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_workflows_stages_permissions_lnk
    ADD CONSTRAINT strapi_workflows_stages_permissions_lnk_fk FOREIGN KEY (workflow_stage_id) REFERENCES public.strapi_workflows_stages(id) ON DELETE CASCADE;


--
-- Name: strapi_workflows_stages_permissions_lnk strapi_workflows_stages_permissions_lnk_ifk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_workflows_stages_permissions_lnk
    ADD CONSTRAINT strapi_workflows_stages_permissions_lnk_ifk FOREIGN KEY (permission_id) REFERENCES public.admin_permissions(id) ON DELETE CASCADE;


--
-- Name: strapi_workflows_stages strapi_workflows_stages_updated_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_workflows_stages
    ADD CONSTRAINT strapi_workflows_stages_updated_by_id_fk FOREIGN KEY (updated_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: strapi_workflows_stages_workflow_lnk strapi_workflows_stages_workflow_lnk_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_workflows_stages_workflow_lnk
    ADD CONSTRAINT strapi_workflows_stages_workflow_lnk_fk FOREIGN KEY (workflow_stage_id) REFERENCES public.strapi_workflows_stages(id) ON DELETE CASCADE;


--
-- Name: strapi_workflows_stages_workflow_lnk strapi_workflows_stages_workflow_lnk_ifk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_workflows_stages_workflow_lnk
    ADD CONSTRAINT strapi_workflows_stages_workflow_lnk_ifk FOREIGN KEY (workflow_id) REFERENCES public.strapi_workflows(id) ON DELETE CASCADE;


--
-- Name: strapi_workflows strapi_workflows_updated_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.strapi_workflows
    ADD CONSTRAINT strapi_workflows_updated_by_id_fk FOREIGN KEY (updated_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: team_members team_members_created_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.team_members
    ADD CONSTRAINT team_members_created_by_id_fk FOREIGN KEY (created_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: team_members team_members_updated_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.team_members
    ADD CONSTRAINT team_members_updated_by_id_fk FOREIGN KEY (updated_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: up_permissions up_permissions_created_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.up_permissions
    ADD CONSTRAINT up_permissions_created_by_id_fk FOREIGN KEY (created_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: up_permissions_role_lnk up_permissions_role_lnk_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.up_permissions_role_lnk
    ADD CONSTRAINT up_permissions_role_lnk_fk FOREIGN KEY (permission_id) REFERENCES public.up_permissions(id) ON DELETE CASCADE;


--
-- Name: up_permissions_role_lnk up_permissions_role_lnk_ifk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.up_permissions_role_lnk
    ADD CONSTRAINT up_permissions_role_lnk_ifk FOREIGN KEY (role_id) REFERENCES public.up_roles(id) ON DELETE CASCADE;


--
-- Name: up_permissions up_permissions_updated_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.up_permissions
    ADD CONSTRAINT up_permissions_updated_by_id_fk FOREIGN KEY (updated_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: up_roles up_roles_created_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.up_roles
    ADD CONSTRAINT up_roles_created_by_id_fk FOREIGN KEY (created_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: up_roles up_roles_updated_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.up_roles
    ADD CONSTRAINT up_roles_updated_by_id_fk FOREIGN KEY (updated_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: up_users up_users_created_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.up_users
    ADD CONSTRAINT up_users_created_by_id_fk FOREIGN KEY (created_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: up_users_role_lnk up_users_role_lnk_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.up_users_role_lnk
    ADD CONSTRAINT up_users_role_lnk_fk FOREIGN KEY (user_id) REFERENCES public.up_users(id) ON DELETE CASCADE;


--
-- Name: up_users_role_lnk up_users_role_lnk_ifk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.up_users_role_lnk
    ADD CONSTRAINT up_users_role_lnk_ifk FOREIGN KEY (role_id) REFERENCES public.up_roles(id) ON DELETE CASCADE;


--
-- Name: up_users up_users_updated_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.up_users
    ADD CONSTRAINT up_users_updated_by_id_fk FOREIGN KEY (updated_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: upload_folders upload_folders_created_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.upload_folders
    ADD CONSTRAINT upload_folders_created_by_id_fk FOREIGN KEY (created_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- Name: upload_folders_parent_lnk upload_folders_parent_lnk_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.upload_folders_parent_lnk
    ADD CONSTRAINT upload_folders_parent_lnk_fk FOREIGN KEY (folder_id) REFERENCES public.upload_folders(id) ON DELETE CASCADE;


--
-- Name: upload_folders_parent_lnk upload_folders_parent_lnk_ifk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.upload_folders_parent_lnk
    ADD CONSTRAINT upload_folders_parent_lnk_ifk FOREIGN KEY (inv_folder_id) REFERENCES public.upload_folders(id) ON DELETE CASCADE;


--
-- Name: upload_folders upload_folders_updated_by_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.upload_folders
    ADD CONSTRAINT upload_folders_updated_by_id_fk FOREIGN KEY (updated_by_id) REFERENCES public.admin_users(id) ON DELETE SET NULL;


--
-- PostgreSQL database dump complete
--

